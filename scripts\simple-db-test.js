// 简单的数据库连接测试
const mysql = require('mysql2/promise')

async function testConnection() {
  console.log('🔍 测试WSL数据库连接')
  console.log('================================')
  
  const config = {
    host: '*************',
    port: 3306,
    user: 'root',
    password: 'cefd14ff2eecd2aa',
    connectTimeout: 60000,
    debug: false
  }
  
  console.log('连接配置:')
  console.log(`主机: ${config.host}`)
  console.log(`端口: ${config.port}`)
  console.log(`用户: ${config.user}`)
  console.log(`密码: ${config.password.substring(0, 4)}****`)
  
  try {
    console.log('\n正在连接...')
    const connection = await mysql.createConnection(config)
    console.log('✅ 连接成功!')
    
    console.log('\n测试ping...')
    await connection.ping()
    console.log('✅ Ping成功!')
    
    console.log('\n测试查询...')
    const [rows] = await connection.query('SELECT 1 as test, VERSION() as version, NOW() as time')
    console.log('✅ 查询成功!')
    console.log('结果:', rows[0])
    
    console.log('\n检查数据库...')
    const [databases] = await connection.query('SHOW DATABASES')
    console.log('✅ 数据库列表:')
    databases.forEach(db => console.log(`  - ${db.Database}`))
    
    // 检查dianfuto数据库是否存在
    const dianfutoExists = databases.some(db => db.Database === 'dianfuto')
    if (dianfutoExists) {
      console.log('\n✅ dianfuto数据库已存在')
      
      // 切换到dianfuto数据库
      await connection.query('USE dianfuto')
      console.log('✅ 成功切换到dianfuto数据库')
      
      // 检查表
      const [tables] = await connection.query('SHOW TABLES')
      console.log(`✅ 找到 ${tables.length} 个表:`)
      tables.forEach(table => console.log(`  - ${Object.values(table)[0]}`))
      
    } else {
      console.log('\n⚠️  dianfuto数据库不存在，需要创建')
    }
    
    await connection.end()
    console.log('\n✅ 连接已关闭')
    
    console.log('\n🎉 数据库连接测试完全成功!')
    console.log('📋 推荐在安装程序中使用以下配置:')
    console.log(`数据库主机: ${config.host}`)
    console.log(`端口: ${config.port}`)
    console.log(`用户名: ${config.user}`)
    console.log(`密码: ${config.password}`)
    console.log(`数据库名: dianfuto`)
    
    return true
    
  } catch (error) {
    console.error('\n❌ 连接失败!')
    console.error('错误代码:', error.code)
    console.error('错误信息:', error.message)
    
    if (error.code === 'ETIMEDOUT') {
      console.log('\n💡 超时解决建议:')
      console.log('1. 检查网络连接')
      console.log('2. 增加连接超时时间')
      console.log('3. 检查MySQL服务器配置')
    } else if (error.code === 'ER_ACCESS_DENIED_ERROR') {
      console.log('\n💡 访问拒绝解决建议:')
      console.log('1. 检查用户名和密码')
      console.log('2. 检查用户权限')
      console.log('3. 确认用户可以从当前IP访问')
    } else if (error.code === 'ECONNREFUSED') {
      console.log('\n💡 连接拒绝解决建议:')
      console.log('1. 检查MySQL服务是否运行')
      console.log('2. 检查端口是否正确')
      console.log('3. 检查防火墙设置')
    }
    
    return false
  }
}

// 运行测试
testConnection().then(success => {
  if (success) {
    console.log('\n✅ 测试完成 - 数据库连接正常')
  } else {
    console.log('\n❌ 测试完成 - 数据库连接失败')
  }
}).catch(error => {
  console.error('\n💥 测试过程中发生严重错误:', error)
})
