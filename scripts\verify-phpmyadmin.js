// 验证phpMyAdmin访问的脚本
const http = require('http')
const https = require('https')

function testHttpAccess(url) {
  return new Promise((resolve, reject) => {
    const protocol = url.startsWith('https') ? https : http
    
    const req = protocol.get(url, (res) => {
      console.log(`✅ HTTP状态: ${res.statusCode}`)
      console.log(`✅ 响应头:`, res.headers)
      
      let data = ''
      res.on('data', chunk => data += chunk)
      res.on('end', () => {
        if (data.includes('phpMyAdmin') || data.includes('mysql') || data.includes('login')) {
          console.log('✅ 响应包含phpMyAdmin相关内容')
          resolve(true)
        } else {
          console.log('⚠️  响应不包含phpMyAdmin内容')
          console.log('响应内容前200字符:', data.substring(0, 200))
          resolve(false)
        }
      })
    })
    
    req.on('error', (error) => {
      console.log(`❌ HTTP请求失败: ${error.message}`)
      reject(error)
    })
    
    req.setTimeout(10000, () => {
      console.log('❌ HTTP请求超时')
      req.destroy()
      reject(new Error('Timeout'))
    })
  })
}

async function verifyPhpMyAdmin() {
  console.log('🔍 验证phpMyAdmin访问')
  console.log('================================')
  
  const phpMyAdminUrl = 'http://172.31.97.108:22929'
  
  console.log(`测试URL: ${phpMyAdminUrl}`)
  
  try {
    const accessible = await testHttpAccess(phpMyAdminUrl)
    
    if (accessible) {
      console.log('\n✅ phpMyAdmin可以访问')
      console.log('这说明MySQL服务器在该地址和端口上运行')
      console.log('但可能存在以下问题:')
      console.log('1. MySQL配置不允许远程连接')
      console.log('2. 用户权限设置问题')
      console.log('3. SSL/TLS配置问题')
      console.log('4. 认证插件兼容性问题')
    } else {
      console.log('\n❌ phpMyAdmin无法访问')
      console.log('可能的原因:')
      console.log('1. 地址或端口错误')
      console.log('2. 服务未运行')
      console.log('3. 防火墙阻止')
    }
    
  } catch (error) {
    console.log('\n❌ 访问测试失败')
    console.log('错误:', error.message)
    
    if (error.code === 'ECONNREFUSED') {
      console.log('连接被拒绝 - 服务可能未运行')
    } else if (error.code === 'ETIMEDOUT') {
      console.log('连接超时 - 网络或防火墙问题')
    } else if (error.code === 'ENOTFOUND') {
      console.log('主机无法解析 - 地址错误')
    }
  }
  
  console.log('\n🔧 建议的解决方案:')
  console.log('1. 确认phpMyAdmin的实际访问地址')
  console.log('2. 检查MySQL的bind-address配置')
  console.log('3. 确认MySQL用户权限设置')
  console.log('4. 尝试使用不同的认证方式')
  
  console.log('\n📝 MySQL配置建议:')
  console.log('在MySQL中执行以下命令:')
  console.log("ALTER USER 'root'@'%' IDENTIFIED WITH mysql_native_password BY 'cefd14ff2eecd2aa';")
  console.log("GRANT ALL PRIVILEGES ON *.* TO 'root'@'%';")
  console.log("FLUSH PRIVILEGES;")
}

// 运行验证
verifyPhpMyAdmin().catch(error => {
  console.error('验证过程中发生错误:', error)
})
