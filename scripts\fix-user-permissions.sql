-- 修复用户权限问题
-- 删除现有的root用户（除了localhost）
DELETE FROM mysql.user WHERE User = 'root' AND Host != 'localhost';

-- 重新创建root用户，支持所有主机
CREATE USER 'root'@'%' IDENTIFIED BY 'cefd14ff2eecd2aa';
CREATE USER 'root'@'127.0.0.1' IDENTIFIED BY 'cefd14ff2eecd2aa';

-- 确保localhost用户密码正确
ALTER USER 'root'@'localhost' IDENTIFIED BY 'cefd14ff2eecd2aa';

-- 授予所有权限
GRANT ALL PRIVILEGES ON *.* TO 'root'@'localhost' WITH GRANT OPTION;
GRANT ALL PRIVILEGES ON *.* TO 'root'@'%' WITH GRANT OPTION;
GRANT ALL PRIVILEGES ON *.* TO 'root'@'127.0.0.1' WITH GRANT OPTION;

-- 刷新权限
FLUSH PRIVILEGES;

-- 显示用户信息
SELECT User, Host, plugin FROM mysql.user WHERE User = 'root';
