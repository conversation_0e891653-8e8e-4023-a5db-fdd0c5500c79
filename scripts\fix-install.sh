#!/bin/bash

# 滇护通安装问题修复脚本
# 用于解决常见的安装和配置问题

echo "🔧 滇护通安装问题修复脚本"
echo "================================"

# 检查当前目录
if [ ! -f "package.json" ]; then
    echo "❌ 错误: 请在项目根目录运行此脚本"
    exit 1
fi

echo "📍 当前目录: $(pwd)"

# 1. 检查Node.js和npm
echo ""
echo "1. 检查Node.js环境..."
if command -v node &> /dev/null; then
    echo "✅ Node.js版本: $(node --version)"
else
    echo "❌ Node.js未安装"
    exit 1
fi

if command -v npm &> /dev/null; then
    echo "✅ npm版本: $(npm --version)"
else
    echo "❌ npm未安装"
    exit 1
fi

# 2. 检查MySQL
echo ""
echo "2. 检查MySQL服务..."
if command -v mysql &> /dev/null; then
    echo "✅ MySQL已安装: $(mysql --version)"
    
    # 检查MySQL服务状态
    if systemctl is-active --quiet mysql; then
        echo "✅ MySQL服务正在运行"
    else
        echo "⚠️  MySQL服务未运行，尝试启动..."
        sudo systemctl start mysql
        if systemctl is-active --quiet mysql; then
            echo "✅ MySQL服务启动成功"
        else
            echo "❌ MySQL服务启动失败"
        fi
    fi
else
    echo "❌ MySQL未安装"
fi

# 3. 清理和重新安装依赖
echo ""
echo "3. 重新安装项目依赖..."
if [ -d "node_modules" ]; then
    echo "🗑️  删除现有node_modules..."
    rm -rf node_modules
fi

if [ -f "package-lock.json" ]; then
    echo "🗑️  删除package-lock.json..."
    rm package-lock.json
fi

if [ -f "yarn.lock" ]; then
    echo "🗑️  删除yarn.lock..."
    rm yarn.lock
fi

echo "📦 安装依赖..."
npm install

if [ $? -eq 0 ]; then
    echo "✅ 依赖安装成功"
else
    echo "❌ 依赖安装失败"
    exit 1
fi

# 4. 构建项目
echo ""
echo "4. 构建项目..."
npm run build

if [ $? -eq 0 ]; then
    echo "✅ 项目构建成功"
else
    echo "❌ 项目构建失败"
    exit 1
fi

# 5. 检查环境配置
echo ""
echo "5. 检查环境配置..."
if [ -f ".env.local" ]; then
    echo "✅ 环境配置文件存在"
    echo "📄 当前配置:"
    cat .env.local | grep -v PASSWORD
else
    echo "⚠️  环境配置文件不存在"
    echo "💡 请运行安装程序创建配置文件"
fi

# 6. 设置文件权限
echo ""
echo "6. 设置文件权限..."
chmod +x scripts/*.sh
chmod +x scripts/*.js
echo "✅ 脚本权限设置完成"

# 7. 创建必要的目录
echo ""
echo "7. 创建必要的目录..."
mkdir -p public/uploads
mkdir -p public/uploads/images
mkdir -p logs
echo "✅ 目录创建完成"

# 8. 测试端口
echo ""
echo "8. 检查端口占用..."
if lsof -i :3000 &> /dev/null; then
    echo "⚠️  端口3000已被占用"
    echo "🔍 占用进程:"
    lsof -i :3000
    echo ""
    echo "💡 请停止占用进程或使用其他端口"
else
    echo "✅ 端口3000可用"
fi

# 9. 生成修复报告
echo ""
echo "9. 生成修复报告..."
cat > fix-report.txt << EOF
滇护通安装修复报告
生成时间: $(date)
项目路径: $(pwd)

系统信息:
- 操作系统: $(uname -a)
- Node.js版本: $(node --version)
- npm版本: $(npm --version)

修复操作:
✅ 重新安装依赖
✅ 重新构建项目
✅ 设置文件权限
✅ 创建必要目录

下一步操作:
1. 启动项目: npm start
2. 访问安装程序: http://localhost:3000/install
3. 按向导完成安装

如果仍有问题，请查看:
- 浏览器控制台错误
- 项目日志文件
- MySQL错误日志
EOF

echo "✅ 修复报告已保存到 fix-report.txt"

echo ""
echo "🎉 修复脚本执行完成！"
echo ""
echo "📋 下一步操作:"
echo "1. 启动项目: npm start"
echo "2. 访问安装程序: http://localhost:3000/install"
echo "3. 按向导完成安装"
echo ""
echo "📞 如果仍有问题，请查看 fix-report.txt 文件"
