// 安装问题诊断脚本
const mysql = require('mysql2/promise')
const { execSync } = require('child_process')
const fs = require('fs')
const path = require('path')

async function diagnoseInstallation() {
  console.log('🔍 滇护通安装问题诊断')
  console.log('================================')
  
  const issues = []
  const suggestions = []
  
  // 1. 检查项目文件
  console.log('\n1. 检查项目文件...')
  try {
    if (!fs.existsSync('package.json')) {
      issues.push('❌ package.json 文件不存在')
      suggestions.push('请确保在项目根目录运行此脚本')
    } else {
      console.log('✅ package.json 存在')
    }
    
    if (!fs.existsSync('app/api/install/test-db/route.ts')) {
      issues.push('❌ 安装API文件缺失')
      suggestions.push('请检查项目文件完整性')
    } else {
      console.log('✅ 安装API文件存在')
    }
    
    if (!fs.existsSync('.next')) {
      issues.push('❌ 项目未构建')
      suggestions.push('请运行: npm run build')
    } else {
      console.log('✅ 项目已构建')
    }
    
  } catch (error) {
    issues.push(`❌ 文件检查失败: ${error.message}`)
  }
  
  // 2. 检查Node.js和依赖
  console.log('\n2. 检查Node.js环境...')
  try {
    const nodeVersion = execSync('node --version', { encoding: 'utf8' }).trim()
    console.log(`✅ Node.js版本: ${nodeVersion}`)
    
    if (!fs.existsSync('node_modules')) {
      issues.push('❌ 依赖未安装')
      suggestions.push('请运行: npm install')
    } else {
      console.log('✅ 依赖已安装')
    }
    
  } catch (error) {
    issues.push(`❌ Node.js检查失败: ${error.message}`)
  }
  
  // 3. 检查MySQL服务
  console.log('\n3. 检查MySQL服务...')
  try {
    const mysqlStatus = execSync('systemctl is-active mysql', { encoding: 'utf8' }).trim()
    if (mysqlStatus === 'active') {
      console.log('✅ MySQL服务正在运行')
    } else {
      issues.push('❌ MySQL服务未运行')
      suggestions.push('请运行: sudo systemctl start mysql')
    }
  } catch (error) {
    issues.push('❌ 无法检查MySQL服务状态')
    suggestions.push('请手动检查MySQL服务: sudo systemctl status mysql')
  }
  
  // 4. 测试数据库连接
  console.log('\n4. 测试数据库连接...')
  const testHosts = ['127.0.0.1', 'localhost', '**********', '*********']
  let connectionFound = false
  
  for (const host of testHosts) {
    try {
      console.log(`🔍 测试主机: ${host}`)
      const connection = await mysql.createConnection({
        host: host,
        port: 3306,
        user: 'root',
        password: '4MndSrzT8TSB7kPz', // 请替换为实际密码
        connectTimeout: 5000,
        acquireTimeout: 5000,
        timeout: 5000,
      })
      
      await connection.ping()
      console.log(`✅ ${host} - 连接成功`)
      connectionFound = true
      
      // 检查数据库
      try {
        await connection.query('USE dianfuto')
        console.log(`✅ ${host} - 数据库 dianfuto 可访问`)
      } catch (dbError) {
        console.log(`⚠️  ${host} - 数据库 dianfuto 不存在，但连接正常`)
      }
      
      await connection.end()
      break
      
    } catch (error) {
      console.log(`❌ ${host} - ${error.message}`)
    }
  }
  
  if (!connectionFound) {
    issues.push('❌ 所有数据库主机都无法连接')
    suggestions.push('请检查MySQL配置和权限设置')
  }
  
  // 5. 检查端口占用
  console.log('\n5. 检查端口占用...')
  try {
    const portCheck = execSync('netstat -tlnp | grep :3000', { encoding: 'utf8' })
    if (portCheck.trim()) {
      console.log('✅ 端口3000正在使用')
      console.log('端口信息:', portCheck.trim())
    } else {
      issues.push('❌ 端口3000未被占用')
      suggestions.push('请启动Next.js项目: npm start')
    }
  } catch (error) {
    issues.push('❌ 无法检查端口状态')
    suggestions.push('请手动检查: netstat -tlnp | grep :3000')
  }
  
  // 6. 检查环境配置
  console.log('\n6. 检查环境配置...')
  if (fs.existsSync('.env.local')) {
    console.log('⚠️  .env.local 已存在，可能已安装')
    const envContent = fs.readFileSync('.env.local', 'utf8')
    if (envContent.includes('DB_HOST')) {
      console.log('✅ 环境配置包含数据库设置')
    }
  } else {
    console.log('✅ .env.local 不存在，可以进行全新安装')
  }
  
  // 7. 生成诊断报告
  console.log('\n================================')
  console.log('📋 诊断报告')
  console.log('================================')
  
  if (issues.length === 0) {
    console.log('🎉 未发现明显问题！')
    console.log('\n📝 建议操作:')
    console.log('1. 确保项目正在运行: npm start')
    console.log('2. 访问安装页面: http://localhost:3000/install')
    console.log('3. 使用正确的数据库主机地址进行安装')
  } else {
    console.log('⚠️  发现以下问题:')
    issues.forEach(issue => console.log(`   ${issue}`))
    
    console.log('\n💡 建议解决方案:')
    suggestions.forEach(suggestion => console.log(`   ${suggestion}`))
  }
  
  console.log('\n🔧 快速修复命令:')
  console.log('# 重新安装依赖并构建')
  console.log('npm install && npm run build')
  console.log('')
  console.log('# 启动MySQL服务')
  console.log('sudo systemctl start mysql')
  console.log('')
  console.log('# 启动项目')
  console.log('npm start')
  console.log('')
  console.log('# 测试数据库连接')
  console.log('node scripts/test-wsl-db.js')
}

// 运行诊断
diagnoseInstallation().catch(error => {
  console.error('诊断过程中发生错误:', error)
})
