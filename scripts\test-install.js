// 测试安装程序的脚本
const mysql = require('mysql2/promise')
require('dotenv').config({ path: '.env.local' })

async function testInstallation() {
  console.log('🔍 开始测试安装程序...')
  
  try {
    // 1. 测试数据库连接
    console.log('\n1. 测试数据库连接...')
    const connection = await mysql.createConnection({
      host: process.env.DB_HOST || 'localhost',
      user: process.env.DB_USER || 'root',
      password: process.env.DB_PASSWORD || '',
      database: process.env.DB_NAME || 'dianfuto',
      port: parseInt(process.env.DB_PORT || '3306'),
    })

    console.log('✅ 数据库连接成功')
    
    // 2. 检查表是否存在
    console.log('\n2. 检查数据库表...')
    const [tables] = await connection.query('SHOW TABLES')
    const tableNames = tables.map(row => Object.values(row)[0])
    
    const expectedTables = [
      'site_settings',
      'images', 
      'cooperation_applications',
      'faqs',
      'faq_categories',
      'prefecture_status',
      'admins',
      'system_logs'
    ]
    
    console.log('现有表:', tableNames)
    
    const missingTables = expectedTables.filter(table => !tableNames.includes(table))
    if (missingTables.length > 0) {
      console.log('❌ 缺少表:', missingTables)
    } else {
      console.log('✅ 所有必需的表都存在')
    }
    
    // 3. 检查基础数据
    console.log('\n3. 检查基础数据...')
    
    const [settings] = await connection.query('SELECT COUNT(*) as count FROM site_settings')
    console.log(`✅ 网站设置: ${settings[0].count} 条记录`)
    
    const [admins] = await connection.query('SELECT COUNT(*) as count FROM admins')
    console.log(`✅ 管理员账户: ${admins[0].count} 个`)
    
    const [faqs] = await connection.query('SELECT COUNT(*) as count FROM faqs')
    console.log(`✅ FAQ数据: ${faqs[0].count} 条记录`)
    
    const [prefectures] = await connection.query('SELECT COUNT(*) as count FROM prefecture_status')
    console.log(`✅ 地州数据: ${prefectures[0].count} 条记录`)
    
    // 4. 测试API接口
    console.log('\n4. 测试API接口...')
    
    try {
      const fetch = require('node-fetch')
      
      // 测试设置API
      const settingsResponse = await fetch('http://localhost:3000/api/settings')
      if (settingsResponse.ok) {
        console.log('✅ 设置API正常')
      } else {
        console.log('❌ 设置API异常')
      }
      
      // 测试合作申请API
      const cooperationResponse = await fetch('http://localhost:3000/api/cooperation')
      if (cooperationResponse.ok) {
        console.log('✅ 合作申请API正常')
      } else {
        console.log('❌ 合作申请API异常')
      }
      
    } catch (apiError) {
      console.log('⚠️  API测试跳过（需要启动服务器）')
    }
    
    await connection.end()
    
    console.log('\n🎉 安装测试完成！')
    console.log('\n📝 下一步操作:')
    console.log('1. 访问 http://localhost:3000 查看前台')
    console.log('2. 访问 http://localhost:3000/admin 进入管理后台')
    console.log('3. 使用安装时设置的管理员账户登录')
    
  } catch (error) {
    console.error('❌ 安装测试失败:', error.message)
    console.log('\n🔧 可能的解决方案:')
    console.log('1. 检查数据库服务是否启动')
    console.log('2. 检查 .env.local 文件配置')
    console.log('3. 重新运行安装程序')
  }
}

// 运行测试
testInstallation()
