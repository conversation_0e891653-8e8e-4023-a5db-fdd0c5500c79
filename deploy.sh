#!/bin/bash

# 滇护通项目部署脚本
echo "开始部署滇护通项目..."

# 进入项目目录
cd /www/wwwroot/dianfuto

# 检查项目目录
if [ ! -f "package.json" ]; then
    echo "错误：未找到 package.json 文件"
    exit 1
fi

# 安装依赖
echo "安装依赖..."
yarn install || npm install

# 构建项目
echo "构建项目..."
yarn build || npm run build

# 检查构建结果
if [ ! -d ".next" ]; then
    echo "错误：构建失败，未生成 .next 目录"
    exit 1
fi

echo "构建完成！"
echo "现在可以启动项目了"

# 设置权限
chown -R www:www /www/wwwroot/dianfuto
chmod -R 755 /www/wwwroot/dianfuto

echo "部署完成！"
