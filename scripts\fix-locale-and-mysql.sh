#!/bin/bash

echo "🔧 修复WSL locale和MySQL问题"
echo "================================"

# 1. 修复locale问题
echo "1. 修复locale设置..."

# 生成所需的locale
locale-gen en_US.UTF-8
update-locale LANG=en_US.UTF-8

# 设置环境变量
export LANG=en_US.UTF-8
export LC_ALL=en_US.UTF-8

# 写入到profile文件
echo 'export LANG=en_US.UTF-8' >> /etc/profile
echo 'export LC_ALL=en_US.UTF-8' >> /etc/profile

echo "✅ locale设置完成"

# 2. 检查MySQL数据目录
echo ""
echo "2. 检查MySQL数据目录..."
if [ -d "/www/server/data" ]; then
    echo "✅ 宝塔MySQL数据目录存在"
    ls -la /www/server/data/ | head -10
else
    echo "❌ 宝塔MySQL数据目录不存在"
    echo "创建数据目录..."
    mkdir -p /www/server/data
    chown mysql:mysql /www/server/data
fi

# 3. 检查MySQL二进制文件
echo ""
echo "3. 检查MySQL二进制文件..."
if [ -f "/www/server/mysql/bin/mysqld" ]; then
    echo "✅ 宝塔MySQL二进制文件存在"
    /www/server/mysql/bin/mysqld --version
else
    echo "❌ 宝塔MySQL二进制文件不存在"
    echo "尝试查找系统MySQL..."
    which mysqld || echo "系统中没有找到mysqld"
fi

# 4. 尝试启动宝塔MySQL
echo ""
echo "4. 尝试启动宝塔MySQL..."

# 停止可能运行的MySQL进程
pkill mysqld 2>/dev/null || true
sleep 3

# 确保必要目录存在
mkdir -p /run/mysqld
chown mysql:mysql /run/mysqld

# 尝试启动MySQL
if [ -f "/www/server/mysql/bin/mysqld_safe" ]; then
    echo "使用宝塔MySQL启动..."
    /www/server/mysql/bin/mysqld_safe --defaults-file=/etc/my.cnf --datadir=/www/server/data --user=mysql --pid-file=/run/mysqld/mysqld.pid &
    
    # 等待启动
    sleep 10
    
    # 检查是否启动成功
    if ps aux | grep -v grep | grep mysqld > /dev/null; then
        echo "✅ 宝塔MySQL启动成功"
        ps aux | grep mysqld | grep -v grep
        echo ""
        echo "监听端口:"
        netstat -tlnp | grep :3306
    else
        echo "❌ 宝塔MySQL启动失败"
        echo "尝试使用系统MySQL..."
        
        # 尝试系统MySQL
        if which mysqld > /dev/null; then
            systemctl start mysql 2>/dev/null || service mysql start 2>/dev/null
            sleep 5
            
            if ps aux | grep -v grep | grep mysqld > /dev/null; then
                echo "✅ 系统MySQL启动成功"
            else
                echo "❌ 系统MySQL也启动失败"
            fi
        fi
    fi
else
    echo "宝塔MySQL不存在，尝试系统MySQL..."
    systemctl start mysql 2>/dev/null || service mysql start 2>/dev/null
fi

# 5. 测试数据库连接
echo ""
echo "5. 测试数据库连接..."

# 等待MySQL完全启动
sleep 5

# 尝试不同的连接方式
echo "测试本地连接..."
if mysql -u root -pcefd14ff2eecd2aa -e "SELECT 'MySQL连接成功' as result;" 2>/dev/null; then
    echo "✅ MySQL本地连接成功"
    
    # 设置用户权限
    echo "设置用户权限..."
    mysql -u root -pcefd14ff2eecd2aa << 'EOF'
CREATE USER IF NOT EXISTS 'root'@'%' IDENTIFIED BY 'cefd14ff2eecd2aa';
CREATE USER IF NOT EXISTS 'root'@'127.0.0.1' IDENTIFIED BY 'cefd14ff2eecd2aa';
GRANT ALL PRIVILEGES ON *.* TO 'root'@'localhost' WITH GRANT OPTION;
GRANT ALL PRIVILEGES ON *.* TO 'root'@'%' WITH GRANT OPTION;
GRANT ALL PRIVILEGES ON *.* TO 'root'@'127.0.0.1' WITH GRANT OPTION;
FLUSH PRIVILEGES;
EOF
    echo "✅ 用户权限设置完成"
    
else
    echo "❌ MySQL连接失败"
    echo "尝试无密码连接..."
    if mysql -u root -e "SELECT 'MySQL无密码连接成功' as result;" 2>/dev/null; then
        echo "✅ MySQL无密码连接成功，设置密码..."
        mysql -u root << 'EOF'
ALTER USER 'root'@'localhost' IDENTIFIED BY 'cefd14ff2eecd2aa';
CREATE USER IF NOT EXISTS 'root'@'%' IDENTIFIED BY 'cefd14ff2eecd2aa';
CREATE USER IF NOT EXISTS 'root'@'127.0.0.1' IDENTIFIED BY 'cefd14ff2eecd2aa';
GRANT ALL PRIVILEGES ON *.* TO 'root'@'localhost' WITH GRANT OPTION;
GRANT ALL PRIVILEGES ON *.* TO 'root'@'%' WITH GRANT OPTION;
GRANT ALL PRIVILEGES ON *.* TO 'root'@'127.0.0.1' WITH GRANT OPTION;
FLUSH PRIVILEGES;
EOF
        echo "✅ 密码和权限设置完成"
    else
        echo "❌ 所有连接方式都失败"
    fi
fi

# 6. 最终状态检查
echo ""
echo "6. 最终状态检查..."
echo "MySQL进程:"
ps aux | grep mysql | grep -v grep || echo "没有MySQL进程运行"

echo ""
echo "监听端口:"
netstat -tlnp | grep :3306 || echo "3306端口未监听"

echo ""
echo "🎉 修复完成！"
echo "================================"

# 7. 测试连接
echo "7. 最终连接测试..."
if mysql -u root -pcefd14ff2eecd2aa -e "SELECT VERSION() as version, NOW() as time;" 2>/dev/null; then
    echo "✅ 数据库连接测试成功！"
    echo ""
    echo "数据库配置信息:"
    echo "主机: localhost 或 127.0.0.1"
    echo "端口: 3306"
    echo "用户名: root"
    echo "密码: cefd14ff2eecd2aa"
else
    echo "❌ 数据库连接测试失败"
fi
