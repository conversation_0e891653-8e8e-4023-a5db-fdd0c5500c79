import { NextRequest, NextResponse } from 'next/server'
import mysql from 'mysql2/promise'

// POST - 测试数据库连接
export async function POST(request: NextRequest) {
  let connection: mysql.Connection | null = null

  try {
    const { host, port, user, password, database } = await request.json()

    // 验证必填字段
    if (!host || !port || !user || !database) {
      return NextResponse.json({
        success: false,
        error: '请填写完整的数据库连接信息'
      }, { status: 400 })
    }

    // 验证数据库名安全性
    if (!/^[a-zA-Z0-9_]+$/.test(database)) {
      return NextResponse.json({
        success: false,
        error: '数据库名只能包含字母、数字和下划线'
      }, { status: 400 })
    }

    console.log('开始测试数据库连接...', { host, port, user, database })

    // 步骤1: 先连接到MySQL服务器（不指定数据库）
    console.log('尝试连接到MySQL服务器...', { host: host.trim(), port: parseInt(port.toString()), user: user.trim() })

    // 对于WSL环境，尝试多种连接方式
    const connectionConfigs = [
      {
        host: host.trim(),
        port: parseInt(port.toString()),
        user: user.trim(),
        password: password || '',
        connectTimeout: 30000,
        charset: 'utf8mb4',
        ssl: false
      },
      // 如果是*************，尝试3306端口
      ...(host.trim() === '*************' ? [{
        host: '*************',
        port: 3306,
        user: user.trim(),
        password: password || '',
        connectTimeout: 30000,
        charset: 'utf8mb4',
        ssl: false
      }] : [])
    ]

    let lastError = null
    for (const config of connectionConfigs) {
      try {
        console.log(`尝试连接配置: ${config.host}:${config.port}`)
        connection = await mysql.createConnection(config)
        console.log(`✅ 连接成功: ${config.host}:${config.port}`)
        break
      } catch (error) {
        console.log(`❌ 连接失败: ${config.host}:${config.port} - ${error.message}`)
        lastError = error
        if (connection) {
          try { await connection.end() } catch {}
          connection = null
        }
      }
    }

    if (!connection) {
      throw lastError || new Error('所有连接配置都失败')
    }

    console.log('MySQL服务器连接成功')

    // 步骤2: 测试基本连接
    await connection.ping()
    console.log('数据库ping测试成功')

    // 步骤3: 检查并创建数据库
    let databaseExists = false

    try {
      // 尝试选择数据库
      await connection.query(`USE \`${database}\``)
      databaseExists = true
      console.log('数据库已存在')
    } catch (useError) {
      console.log('数据库不存在，尝试创建...')
      try {
        // 创建数据库
        await connection.query(`CREATE DATABASE \`${database}\` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci`)
        await connection.query(`USE \`${database}\``)
        databaseExists = true
        console.log('数据库创建成功')
      } catch (createError: any) {
        console.error('创建数据库失败:', createError)
        return NextResponse.json({
          success: false,
          error: `无法创建数据库 "${database}"。错误: ${createError.message}`
        }, { status: 400 })
      }
    }

    // 步骤4: 执行简单查询验证
    const [rows] = await connection.query('SELECT 1 as test, NOW() as current_time')
    console.log('数据库查询测试成功:', rows)

    return NextResponse.json({
      success: true,
      message: '数据库连接测试成功！',
      databaseExists,
      info: {
        host,
        port: parseInt(port.toString()),
        database,
        user,
        testQuery: rows
      }
    })

  } catch (error: any) {
    console.error('数据库连接测试失败:', {
      code: error.code,
      message: error.message,
      errno: error.errno,
      sqlState: error.sqlState,
      sqlMessage: error.sqlMessage,
      stack: error.stack
    })

    let errorMessage = '数据库连接失败'
    let errorCode = error.code || 'UNKNOWN'

    // 根据错误类型提供具体的错误信息
    switch (error.code) {
      case 'ECONNREFUSED':
        errorMessage = `无法连接到数据库服务器 ${error.address || host}:${error.port || port}。请检查MySQL服务是否启动，或尝试使用不同的主机地址（如127.0.0.1）。`
        break
      case 'ER_ACCESS_DENIED_ERROR':
        errorMessage = `数据库访问被拒绝。请检查用户名"${user}"和密码是否正确。`
        break
      case 'ER_BAD_DB_ERROR':
        errorMessage = `数据库 "${database}" 不存在，但创建失败。`
        break
      case 'ETIMEDOUT':
      case 'ENOTFOUND':
        errorMessage = `数据库连接超时或主机"${host}"无法解析。在WSL环境中，请尝试使用127.0.0.1或运行test-wsl-db.js脚本找到正确的主机地址。`
        break
      case 'ER_DBACCESS_DENIED_ERROR':
        errorMessage = `用户"${user}"没有访问数据库的权限。`
        break
      case 'ECONNRESET':
        errorMessage = '数据库连接被重置。请检查MySQL服务器配置。'
        break
      default:
        errorMessage = error.message || '未知的数据库连接错误'
        if (error.message?.includes('timeout')) {
          errorMessage += ' (连接超时，请检查网络和数据库服务器状态)'
        }
    }

    return NextResponse.json({
      success: false,
      error: errorMessage,
      code: errorCode,
      details: error.message,
      suggestion: errorCode === 'ECONNREFUSED' || errorCode === 'ETIMEDOUT' || errorCode === 'ENOTFOUND'
        ? '建议运行: node scripts/test-wsl-db.js 来找到正确的数据库主机地址'
        : null
    }, { status: 400 })

  } finally {
    // 确保连接被正确关闭
    if (connection) {
      try {
        await connection.end()
        console.log('数据库连接已关闭')
      } catch (closeError) {
        console.error('关闭数据库连接时出错:', closeError)
      }
    }
  }
}
