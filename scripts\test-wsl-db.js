// WSL环境数据库连接测试脚本
const mysql = require('mysql2/promise')
const { execSync } = require('child_process')

async function testWSLDatabase() {
  console.log('🔍 WSL环境数据库连接测试')
  console.log('================================')
  
  // 1. 检测WSL环境
  console.log('\n1. 检测环境信息...')
  try {
    const hostname = execSync('hostname', { encoding: 'utf8' }).trim()
    const wslVersion = execSync('cat /proc/version', { encoding: 'utf8' })
    console.log('✅ 主机名:', hostname)
    console.log('✅ WSL版本:', wslVersion.includes('microsoft') ? 'WSL' : '可能不是WSL环境')
  } catch (error) {
    console.log('⚠️  无法检测环境信息')
  }
  
  // 2. 获取网络信息
  console.log('\n2. 获取网络信息...')
  try {
    const ipInfo = execSync('ip route show default', { encoding: 'utf8' })
    console.log('✅ 默认路由:', ipInfo.trim())
    
    const interfaces = execSync('ip addr show', { encoding: 'utf8' })
    const ethMatch = interfaces.match(/inet (\d+\.\d+\.\d+\.\d+).*eth0/)
    if (ethMatch) {
      console.log('✅ WSL IP地址:', ethMatch[1])
    }
  } catch (error) {
    console.log('⚠️  无法获取网络信息')
  }
  
  // 3. 测试不同的数据库主机地址
  const hostOptions = [
    'localhost',
    '127.0.0.1',
    '**********',    // Docker默认网关
    '**********',    // 另一个常见的Docker网关
    '************',  // Docker Desktop for Windows
    '*********',     // WSL2常见网关
  ]
  
  console.log('\n3. 测试数据库连接...')
  
  for (const host of hostOptions) {
    console.log(`\n🔍 测试主机: ${host}`)
    
    try {
      const connection = await mysql.createConnection({
        host: host,
        port: 3306,
        user: 'root',
        password: '4MndSrzT8TSB7kPz', // 从之前的脚本中看到的密码
        connectTimeout: 5000,
        acquireTimeout: 5000,
        timeout: 5000,
      })
      
      await connection.ping()
      console.log(`✅ ${host} - 连接成功！`)
      
      // 测试数据库操作
      try {
        await connection.query('USE dianfuto')
        console.log(`✅ ${host} - 数据库 'dianfuto' 可访问`)
        
        const [tables] = await connection.query('SHOW TABLES')
        console.log(`✅ ${host} - 找到 ${tables.length} 个表`)
        
        await connection.end()
        
        // 找到可用的主机，输出配置建议
        console.log('\n🎉 找到可用的数据库主机！')
        console.log('📋 推荐的数据库配置:')
        console.log(`数据库主机: ${host}`)
        console.log('端口: 3306')
        console.log('数据库名: dianfuto')
        console.log('用户名: root')
        console.log('密码: [您的MySQL密码]')
        
        return host
        
      } catch (dbError) {
        console.log(`⚠️  ${host} - 连接成功但数据库操作失败:`, dbError.message)
        await connection.end()
      }
      
    } catch (error) {
      if (error.code === 'ECONNREFUSED') {
        console.log(`❌ ${host} - 连接被拒绝`)
      } else if (error.code === 'ETIMEDOUT') {
        console.log(`❌ ${host} - 连接超时`)
      } else if (error.code === 'ER_ACCESS_DENIED_ERROR') {
        console.log(`❌ ${host} - 访问被拒绝（用户名或密码错误）`)
      } else {
        console.log(`❌ ${host} - 连接失败:`, error.message)
      }
    }
  }
  
  console.log('\n❌ 所有主机地址都无法连接')
  console.log('\n🔧 故障排除建议:')
  console.log('1. 确保MySQL服务正在运行:')
  console.log('   sudo systemctl status mysql')
  console.log('   sudo systemctl start mysql')
  console.log('')
  console.log('2. 检查MySQL绑定地址:')
  console.log('   sudo nano /etc/mysql/mysql.conf.d/mysqld.cnf')
  console.log('   确保 bind-address = 0.0.0.0 或注释掉该行')
  console.log('')
  console.log('3. 重启MySQL服务:')
  console.log('   sudo systemctl restart mysql')
  console.log('')
  console.log('4. 检查防火墙设置:')
  console.log('   sudo ufw status')
  console.log('   sudo ufw allow 3306')
  console.log('')
  console.log('5. 创建远程访问用户:')
  console.log('   mysql -u root -p')
  console.log("   CREATE USER 'root'@'%' IDENTIFIED BY 'your_password';")
  console.log("   GRANT ALL PRIVILEGES ON *.* TO 'root'@'%';")
  console.log('   FLUSH PRIVILEGES;')
  
  return null
}

// 运行测试
testWSLDatabase().then(result => {
  if (result) {
    console.log(`\n✅ 成功！请在安装程序中使用主机地址: ${result}`)
  } else {
    console.log('\n❌ 未找到可用的数据库连接')
  }
}).catch(error => {
  console.error('测试过程中发生错误:', error)
})
