#!/bin/bash

echo "🔧 修复WSL MySQL配置"
echo "================================"

# 检查是否为root用户
if [ "$EUID" -ne 0 ]; then
    echo "⚠️  此脚本需要root权限"
    echo "请使用: sudo bash scripts/fix-mysql-wsl.sh"
    exit 1
fi

echo "📍 当前用户: $(whoami)"
echo "📍 当前目录: $(pwd)"

# 1. 检查MySQL服务状态
echo ""
echo "1. 检查MySQL服务状态..."
if systemctl is-active --quiet mysql; then
    echo "✅ MySQL服务正在运行"
else
    echo "❌ MySQL服务未运行，正在启动..."
    systemctl start mysql
    if systemctl is-active --quiet mysql; then
        echo "✅ MySQL服务启动成功"
    else
        echo "❌ MySQL服务启动失败"
        exit 1
    fi
fi

# 2. 检查当前监听状态
echo ""
echo "2. 检查MySQL监听状态..."
echo "当前监听端口:"
netstat -tlnp | grep :3306 || echo "❌ MySQL未监听3306端口"

# 3. 备份并修改MySQL配置
echo ""
echo "3. 修改MySQL配置..."
MYSQL_CONFIG="/etc/mysql/mysql.conf.d/mysqld.cnf"

if [ -f "$MYSQL_CONFIG" ]; then
    echo "✅ 找到MySQL配置文件: $MYSQL_CONFIG"
    
    # 备份配置文件
    cp "$MYSQL_CONFIG" "$MYSQL_CONFIG.backup.$(date +%Y%m%d_%H%M%S)"
    echo "✅ 配置文件已备份"
    
    # 检查并修改bind-address
    if grep -q "^bind-address" "$MYSQL_CONFIG"; then
        echo "当前bind-address设置:"
        grep "^bind-address" "$MYSQL_CONFIG"
        
        # 修改为监听所有地址
        sed -i 's/^bind-address.*/bind-address = 0.0.0.0/' "$MYSQL_CONFIG"
        echo "✅ bind-address已修改为0.0.0.0"
    else
        echo "⚠️  未找到bind-address设置，添加配置..."
        echo "" >> "$MYSQL_CONFIG"
        echo "# Allow connections from all addresses" >> "$MYSQL_CONFIG"
        echo "bind-address = 0.0.0.0" >> "$MYSQL_CONFIG"
        echo "✅ 已添加bind-address = 0.0.0.0"
    fi
    
    # 添加其他必要配置
    if ! grep -q "skip-networking" "$MYSQL_CONFIG"; then
        echo "" >> "$MYSQL_CONFIG"
        echo "# Ensure networking is enabled" >> "$MYSQL_CONFIG"
        echo "# skip-networking" >> "$MYSQL_CONFIG"
        echo "✅ 已确保网络功能启用"
    fi
    
else
    echo "❌ 未找到MySQL配置文件"
    exit 1
fi

# 4. 重启MySQL服务
echo ""
echo "4. 重启MySQL服务..."
systemctl restart mysql

if systemctl is-active --quiet mysql; then
    echo "✅ MySQL服务重启成功"
else
    echo "❌ MySQL服务重启失败"
    systemctl status mysql --no-pager -l
    exit 1
fi

# 5. 验证监听状态
echo ""
echo "5. 验证MySQL监听状态..."
sleep 3
echo "新的监听状态:"
netstat -tlnp | grep :3306

if netstat -tlnp | grep -q "0.0.0.0:3306"; then
    echo "✅ MySQL现在监听所有IPv4地址"
elif netstat -tlnp | grep -q "127.0.0.1:3306"; then
    echo "⚠️  MySQL只监听localhost"
else
    echo "❌ MySQL监听状态异常"
fi

# 6. 设置用户权限
echo ""
echo "6. 设置MySQL用户权限..."
echo "请输入MySQL root密码:"
read -s MYSQL_ROOT_PASSWORD

mysql -u root -p"$MYSQL_ROOT_PASSWORD" << EOF
-- 创建或更新root用户权限
CREATE USER IF NOT EXISTS 'root'@'%' IDENTIFIED BY '$MYSQL_ROOT_PASSWORD';
GRANT ALL PRIVILEGES ON *.* TO 'root'@'%' WITH GRANT OPTION;

-- 确保本地访问正常
GRANT ALL PRIVILEGES ON *.* TO 'root'@'localhost' WITH GRANT OPTION;
GRANT ALL PRIVILEGES ON *.* TO 'root'@'127.0.0.1' WITH GRANT OPTION;

-- 使用兼容的认证插件
ALTER USER 'root'@'%' IDENTIFIED WITH mysql_native_password BY '$MYSQL_ROOT_PASSWORD';
ALTER USER 'root'@'localhost' IDENTIFIED WITH mysql_native_password BY '$MYSQL_ROOT_PASSWORD';

-- 刷新权限
FLUSH PRIVILEGES;

-- 显示用户信息
SELECT User, Host, plugin FROM mysql.user WHERE User = 'root';
EOF

if [ $? -eq 0 ]; then
    echo "✅ MySQL用户权限设置成功"
else
    echo "❌ MySQL用户权限设置失败"
fi

# 7. 测试连接
echo ""
echo "7. 测试数据库连接..."

# 获取WSL的IP地址
WSL_IP=$(ip route get 1 | awk '{print $7; exit}')
echo "WSL IP地址: $WSL_IP"

# 测试不同的连接方式
echo "测试本地连接..."
if mysql -h 127.0.0.1 -u root -p"$MYSQL_ROOT_PASSWORD" -e "SELECT 1;" &>/dev/null; then
    echo "✅ 127.0.0.1 连接成功"
else
    echo "❌ 127.0.0.1 连接失败"
fi

if mysql -h localhost -u root -p"$MYSQL_ROOT_PASSWORD" -e "SELECT 1;" &>/dev/null; then
    echo "✅ localhost 连接成功"
else
    echo "❌ localhost 连接失败"
fi

if mysql -h "$WSL_IP" -u root -p"$MYSQL_ROOT_PASSWORD" -e "SELECT 1;" &>/dev/null; then
    echo "✅ $WSL_IP 连接成功"
else
    echo "❌ $WSL_IP 连接失败"
fi

# 8. 生成报告
echo ""
echo "================================"
echo "📋 修复完成报告"
echo "================================"
echo "修复时间: $(date)"
echo ""
echo "✅ 已执行的操作:"
echo "  - 修改bind-address为0.0.0.0"
echo "  - 重启MySQL服务"
echo "  - 设置用户权限"
echo "  - 配置认证插件"
echo ""
echo "📝 推荐的数据库配置:"
echo "  主机: 127.0.0.1 或 $WSL_IP"
echo "  端口: 3306"
echo "  用户名: root"
echo "  密码: [您刚才输入的密码]"
echo "  数据库名: dianfuto"
echo ""
echo "🎉 MySQL配置修复完成！"
