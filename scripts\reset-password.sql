-- 重置root用户密码
ALTER USER 'root'@'localhost' IDENTIFIED BY 'cefd14ff2eecd2aa';
ALTER USER 'root'@'127.0.0.1' IDENTIFIED BY 'cefd14ff2eecd2aa';
ALTER USER 'root'@'%' IDENTIFIED BY 'cefd14ff2eecd2aa';

-- 确保权限正确
GRANT ALL PRIVILEGES ON *.* TO 'root'@'localhost' WITH GRANT OPTION;
GRANT ALL PRIVILEGES ON *.* TO 'root'@'127.0.0.1' WITH GRANT OPTION;
GRANT ALL PRIVILEGES ON *.* TO 'root'@'%' WITH GRANT OPTION;

FLUSH PRIVILEGES;
