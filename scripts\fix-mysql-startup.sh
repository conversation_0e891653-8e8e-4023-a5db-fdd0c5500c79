#!/bin/bash

echo "🔧 修复MySQL启动问题"
echo "================================"

# 检查是否为root用户
if [ "$EUID" -ne 0 ]; then
    echo "⚠️  此脚本需要root权限"
    echo "请使用: sudo bash scripts/fix-mysql-startup.sh"
    exit 1
fi

echo "📍 当前用户: $(whoami)"

# 1. 停止MySQL服务（如果正在运行）
echo ""
echo "1. 停止MySQL服务..."
systemctl stop mysql 2>/dev/null || true
pkill mysqld 2>/dev/null || true
sleep 3

# 2. 创建必要的目录和文件
echo ""
echo "2. 创建必要的目录和文件..."

# 创建PID目录
mkdir -p /run/mysqld
chown mysql:mysql /run/mysqld
chmod 755 /run/mysqld
echo "✅ 创建PID目录: /run/mysqld"

# 创建日志目录
mkdir -p /var/log/mysql
chown mysql:mysql /var/log/mysql
chmod 755 /var/log/mysql
echo "✅ 创建日志目录: /var/log/mysql"

# 创建socket目录
mkdir -p /var/run/mysqld
chown mysql:mysql /var/run/mysqld
chmod 755 /var/run/mysqld
echo "✅ 创建socket目录: /var/run/mysqld"

# 确保数据目录权限正确
chown -R mysql:mysql /var/lib/mysql
echo "✅ 设置数据目录权限"

# 3. 检查并修复配置文件
echo ""
echo "3. 检查MySQL配置文件..."

# 确保配置文件存在
if [ ! -f "/etc/mysql/mariadb.conf.d/50-server.cnf" ]; then
    echo "创建服务器配置文件..."
    cat > /etc/mysql/mariadb.conf.d/50-server.cnf << 'EOF'
[server]

[mysqld]
user                    = mysql
pid-file                = /run/mysqld/mysqld.pid
socket                  = /run/mysqld/mysqld.sock
basedir                 = /usr
datadir                 = /var/lib/mysql
tmpdir                  = /tmp
lc-messages-dir         = /usr/share/mysql
skip-external-locking

# 网络配置
bind-address            = 0.0.0.0
port                    = 3306

# 日志配置
log-error               = /var/log/mysql/error.log
general_log_file        = /var/log/mysql/mysql.log
general_log             = 1

# 字符集
character-set-server    = utf8mb4
collation-server        = utf8mb4_general_ci

[embedded]

[mariadb]

[mariadb-10.6]
EOF
    echo "✅ 创建配置文件"
else
    echo "✅ 配置文件已存在"
fi

# 4. 初始化数据库（如果需要）
echo ""
echo "4. 检查数据库初始化..."
if [ ! -d "/var/lib/mysql/mysql" ]; then
    echo "初始化MySQL数据库..."
    mysql_install_db --user=mysql --datadir=/var/lib/mysql
    echo "✅ 数据库初始化完成"
else
    echo "✅ 数据库已初始化"
fi

# 5. 启动MySQL服务
echo ""
echo "5. 启动MySQL服务..."
systemctl start mysql

# 等待服务启动
sleep 5

# 6. 检查服务状态
echo ""
echo "6. 检查服务状态..."
if systemctl is-active --quiet mysql; then
    echo "✅ MySQL服务启动成功"
    
    # 显示监听端口
    echo ""
    echo "监听端口:"
    netstat -tlnp | grep :3306 || echo "❌ 未监听3306端口"
    
    # 显示进程信息
    echo ""
    echo "MySQL进程:"
    ps aux | grep mysql | grep -v grep
    
else
    echo "❌ MySQL服务启动失败"
    echo ""
    echo "检查错误日志:"
    if [ -f "/var/log/mysql/error.log" ]; then
        tail -20 /var/log/mysql/error.log
    else
        echo "错误日志文件不存在"
    fi
    exit 1
fi

# 7. 设置root密码（如果需要）
echo ""
echo "7. 设置root用户密码..."

# 尝试无密码连接
if mysql -u root -e "SELECT 1;" 2>/dev/null; then
    echo "设置root密码..."
    mysql -u root << EOF
ALTER USER 'root'@'localhost' IDENTIFIED BY 'cefd14ff2eecd2aa';
CREATE USER IF NOT EXISTS 'root'@'%' IDENTIFIED BY 'cefd14ff2eecd2aa';
GRANT ALL PRIVILEGES ON *.* TO 'root'@'localhost' WITH GRANT OPTION;
GRANT ALL PRIVILEGES ON *.* TO 'root'@'%' WITH GRANT OPTION;
FLUSH PRIVILEGES;
EOF
    echo "✅ root密码设置完成"
else
    echo "✅ root用户已有密码"
fi

# 8. 测试连接
echo ""
echo "8. 测试数据库连接..."
if mysql -u root -pcefd14ff2eecd2aa -e "SELECT VERSION();" 2>/dev/null; then
    echo "✅ 数据库连接测试成功"
else
    echo "❌ 数据库连接测试失败"
fi

echo ""
echo "🎉 MySQL修复完成！"
echo "================================"
echo "数据库配置信息:"
echo "主机: localhost 或 127.0.0.1 或 *************"
echo "端口: 3306"
echo "用户名: root"
echo "密码: cefd14ff2eecd2aa"
