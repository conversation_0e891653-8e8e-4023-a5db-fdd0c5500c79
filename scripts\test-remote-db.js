// 远程数据库连接测试脚本
const mysql = require('mysql2/promise')

async function testRemoteDatabase() {
  console.log('🔍 远程数据库连接测试')
  console.log('================================')
  
  // 远程数据库配置
  const dbConfig = {
    host: '*************',
    port: 22929,
    user: 'root',
    password: 'cefd14ff2eecd2aa',
    database: 'dianfuto'
  }
  
  console.log('📋 数据库配置信息:')
  console.log(`   主机: ${dbConfig.host}`)
  console.log(`   端口: ${dbConfig.port}`)
  console.log(`   用户: ${dbConfig.user}`)
  console.log(`   密码: ${dbConfig.password.substring(0, 4)}****`)
  console.log(`   数据库: ${dbConfig.database}`)
  
  // 测试不同的超时配置
  const timeoutConfigs = [
    { timeout: 5000, desc: '5秒超时' },
    { timeout: 10000, desc: '10秒超时' },
    { timeout: 30000, desc: '30秒超时' },
    { timeout: 60000, desc: '60秒超时' }
  ]
  
  for (const config of timeoutConfigs) {
    console.log(`\n🔍 测试: ${config.desc}`)
    
    let connection = null
    try {
      const startTime = Date.now()
      
      // 步骤1: 连接到MySQL服务器（不指定数据库）
      console.log('   步骤1: 连接到MySQL服务器...')
      connection = await mysql.createConnection({
        host: dbConfig.host,
        port: dbConfig.port,
        user: dbConfig.user,
        password: dbConfig.password,
        connectTimeout: config.timeout,
        acquireTimeout: config.timeout,
        timeout: config.timeout,
        charset: 'utf8mb4'
      })
      
      const connectTime = Date.now() - startTime
      console.log(`   ✅ 服务器连接成功 (${connectTime}ms)`)
      
      // 步骤2: 测试ping
      console.log('   步骤2: 测试ping...')
      const pingStart = Date.now()
      await connection.ping()
      const pingTime = Date.now() - pingStart
      console.log(`   ✅ Ping成功 (${pingTime}ms)`)
      
      // 步骤3: 检查数据库是否存在
      console.log('   步骤3: 检查数据库...')
      try {
        const [databases] = await connection.query('SHOW DATABASES LIKE ?', [dbConfig.database])
        if (databases.length > 0) {
          console.log(`   ✅ 数据库 '${dbConfig.database}' 已存在`)
          
          // 尝试使用数据库
          await connection.query(`USE ${dbConfig.database}`)
          console.log(`   ✅ 成功切换到数据库 '${dbConfig.database}'`)
          
          // 检查表
          const [tables] = await connection.query('SHOW TABLES')
          console.log(`   ✅ 数据库中有 ${tables.length} 个表`)
          if (tables.length > 0) {
            console.log('   表列表:', tables.map(t => Object.values(t)[0]).join(', '))
          }
          
        } else {
          console.log(`   ⚠️  数据库 '${dbConfig.database}' 不存在，尝试创建...`)
          await connection.query(`CREATE DATABASE IF NOT EXISTS ${dbConfig.database} CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci`)
          console.log(`   ✅ 数据库 '${dbConfig.database}' 创建成功`)
        }
      } catch (dbError) {
        console.log(`   ❌ 数据库操作失败: ${dbError.message}`)
      }
      
      // 步骤4: 测试简单查询
      console.log('   步骤4: 测试查询...')
      const queryStart = Date.now()
      const [rows] = await connection.query('SELECT 1 as test, NOW() as current_time, VERSION() as mysql_version')
      const queryTime = Date.now() - queryStart
      console.log(`   ✅ 查询成功 (${queryTime}ms)`)
      console.log(`   MySQL版本: ${rows[0].mysql_version}`)
      console.log(`   服务器时间: ${rows[0].current_time}`)
      
      await connection.end()
      
      const totalTime = Date.now() - startTime
      console.log(`\n🎉 远程数据库连接测试成功！`)
      console.log(`📊 总耗时: ${totalTime}ms`)
      console.log(`📋 推荐在安装程序中使用以下配置:`)
      console.log(`   数据库主机: ${dbConfig.host}`)
      console.log(`   端口: ${dbConfig.port}`)
      console.log(`   用户名: ${dbConfig.user}`)
      console.log(`   密码: ${dbConfig.password}`)
      console.log(`   数据库名: ${dbConfig.database}`)
      
      return true
      
    } catch (error) {
      const errorTime = Date.now() - startTime
      console.log(`   ❌ 连接失败 (${errorTime}ms)`)
      console.log(`   错误代码: ${error.code}`)
      console.log(`   错误信息: ${error.message}`)
      
      // 分析错误原因
      switch (error.code) {
        case 'ETIMEDOUT':
          console.log('   🔍 分析: 连接超时')
          console.log('   可能原因:')
          console.log('   - 网络延迟过高')
          console.log('   - 远程服务器响应慢')
          console.log('   - 防火墙阻止连接')
          console.log('   - 端口配置错误')
          break
        case 'ECONNREFUSED':
          console.log('   🔍 分析: 连接被拒绝')
          console.log('   可能原因:')
          console.log('   - 端口号错误')
          console.log('   - 服务器未启动')
          console.log('   - 防火墙阻止')
          break
        case 'ENOTFOUND':
          console.log('   🔍 分析: 主机无法解析')
          console.log('   可能原因:')
          console.log('   - IP地址错误')
          console.log('   - DNS解析问题')
          console.log('   - 网络连接问题')
          break
        case 'ER_ACCESS_DENIED_ERROR':
          console.log('   🔍 分析: 访问被拒绝')
          console.log('   可能原因:')
          console.log('   - 用户名或密码错误')
          console.log('   - 用户没有远程访问权限')
          break
        default:
          console.log(`   🔍 分析: 未知错误 (${error.code})`)
      }
      
      if (connection) {
        try {
          await connection.end()
        } catch (closeError) {
          // 忽略关闭错误
        }
      }
    }
  }
  
  console.log('\n❌ 所有超时配置都失败了')
  console.log('\n🔧 建议的解决步骤:')
  
  console.log('\n1. 检查网络连接:')
  console.log(`   ping ${dbConfig.host}`)
  console.log(`   telnet ${dbConfig.host} ${dbConfig.port}`)
  
  console.log('\n2. 验证数据库配置:')
  console.log('   - 确认IP地址和端口号正确')
  console.log('   - 确认用户名和密码正确')
  console.log('   - 确认数据库服务器允许远程连接')
  
  console.log('\n3. 检查防火墙设置:')
  console.log('   - 本地防火墙是否允许出站连接')
  console.log('   - 远程服务器防火墙是否允许入站连接')
  
  console.log('\n4. 联系数据库管理员:')
  console.log('   - 确认数据库服务器状态')
  console.log('   - 确认用户权限设置')
  console.log('   - 确认网络配置')
  
  return false
}

// 运行测试
console.log('开始远程数据库连接测试...\n')
testRemoteDatabase().then(success => {
  if (success) {
    console.log('\n✅ 测试完成！数据库连接正常')
    console.log('现在可以在安装程序中使用这些配置进行安装')
  } else {
    console.log('\n❌ 测试完成，数据库连接失败')
    console.log('请根据上述建议进行故障排除')
  }
}).catch(error => {
  console.error('\n💥 测试过程中发生严重错误:', error)
  console.log('\n请检查:')
  console.log('1. Node.js是否正确安装')
  console.log('2. mysql2包是否已安装: npm install mysql2')
  console.log('3. 网络连接是否正常')
})
