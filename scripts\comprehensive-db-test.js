// 综合数据库连接诊断脚本
const mysql = require('mysql2/promise')
const { spawn } = require('child_process')
const net = require('net')

// 网络连通性测试
function testNetworkConnectivity(host, port) {
  return new Promise((resolve) => {
    console.log(`🔍 测试网络连通性: ${host}:${port}`)
    
    const socket = new net.Socket()
    const timeout = 10000
    
    socket.setTimeout(timeout)
    
    socket.on('connect', () => {
      console.log(`✅ 网络连接成功: ${host}:${port}`)
      socket.destroy()
      resolve(true)
    })
    
    socket.on('timeout', () => {
      console.log(`❌ 网络连接超时: ${host}:${port}`)
      socket.destroy()
      resolve(false)
    })
    
    socket.on('error', (error) => {
      console.log(`❌ 网络连接错误: ${host}:${port} - ${error.message}`)
      resolve(false)
    })
    
    socket.connect(port, host)
  })
}

// WSL内部MySQL测试
function testWSLInternalMySQL() {
  return new Promise((resolve) => {
    console.log('🔍 测试WSL内部MySQL连接')
    
    const wslCommand = spawn('wsl', ['-e', 'bash', '-c', 'mysql -u root -pcefd14ff2eecd2aa -e "SELECT 1 as test;"'], {
      stdio: ['pipe', 'pipe', 'pipe']
    })
    
    let output = ''
    let errorOutput = ''
    
    wslCommand.stdout.on('data', (data) => {
      output += data.toString()
    })
    
    wslCommand.stderr.on('data', (data) => {
      errorOutput += data.toString()
    })
    
    wslCommand.on('close', (code) => {
      if (code === 0 && output.includes('test')) {
        console.log('✅ WSL内部MySQL连接成功')
        console.log('输出:', output.trim())
        resolve(true)
      } else {
        console.log('❌ WSL内部MySQL连接失败')
        console.log('错误输出:', errorOutput.trim())
        console.log('输出:', output.trim())
        resolve(false)
      }
    })
    
    setTimeout(() => {
      wslCommand.kill()
      console.log('❌ WSL MySQL测试超时')
      resolve(false)
    }, 15000)
  })
}

async function testMultipleConfigurations() {
  console.log('🔍 测试多种数据库配置')
  console.log('================================')
  
  const configurations = [
    {
      name: '原始配置 (22929端口)',
      host: '*************',
      port: 22929,
      user: 'root',
      password: 'cefd14ff2eecd2aa'
    },
    {
      name: '标准MySQL端口 (3306)',
      host: '*************',
      port: 3306,
      user: 'root',
      password: 'cefd14ff2eecd2aa'
    },
    {
      name: 'localhost (3306)',
      host: 'localhost',
      port: 3306,
      user: 'root',
      password: 'cefd14ff2eecd2aa'
    },
    {
      name: '127.0.0.1 (3306)',
      host: '127.0.0.1',
      port: 3306,
      user: 'root',
      password: 'cefd14ff2eecd2aa'
    }
  ]
  
  for (const config of configurations) {
    console.log(`\n📋 测试配置: ${config.name}`)
    console.log(`   主机: ${config.host}:${config.port}`)
    
    // 1. 网络连通性测试
    const networkOk = await testNetworkConnectivity(config.host, config.port)
    
    if (!networkOk) {
      console.log(`❌ ${config.name} - 网络不通`)
      continue
    }
    
    // 2. MySQL连接测试
    try {
      console.log(`🔍 尝试MySQL连接...`)
      const connection = await mysql.createConnection({
        host: config.host,
        port: config.port,
        user: config.user,
        password: config.password,
        connectTimeout: 15000,
        charset: 'utf8mb4'
      })
      
      console.log(`✅ ${config.name} - MySQL连接成功!`)
      
      // 测试查询
      const [rows] = await connection.execute('SELECT VERSION() as version, NOW() as now')
      console.log(`✅ 查询成功:`, rows[0])
      
      await connection.end()
      console.log(`✅ ${config.name} - 测试完成`)
      
      return config // 返回成功的配置
      
    } catch (error) {
      console.log(`❌ ${config.name} - MySQL连接失败`)
      console.log(`   错误: ${error.code} - ${error.message}`)
    }
  }
  
  return null
}

async function main() {
  console.log('🚀 开始综合数据库连接诊断')
  console.log('========================================')
  
  // 1. WSL内部MySQL测试
  console.log('\n📍 步骤1: WSL内部MySQL连接测试')
  const wslInternalOk = await testWSLInternalMySQL()
  
  if (!wslInternalOk) {
    console.log('\n❌ WSL内部MySQL连接失败，请检查：')
    console.log('1. MySQL服务是否在WSL中运行')
    console.log('2. root用户密码是否正确')
    console.log('3. MySQL配置是否正确')
    return
  }
  
  // 2. 多配置测试
  console.log('\n📍 步骤2: 从Windows到WSL的连接测试')
  const successConfig = await testMultipleConfigurations()
  
  if (successConfig) {
    console.log('\n🎉 找到可用的数据库配置!')
    console.log('========================================')
    console.log('✅ 成功配置:')
    console.log(`   主机: ${successConfig.host}`)
    console.log(`   端口: ${successConfig.port}`)
    console.log(`   用户: ${successConfig.user}`)
    console.log(`   密码: ${successConfig.password}`)
    console.log('')
    console.log('请在安装页面使用此配置。')
  } else {
    console.log('\n❌ 所有配置都失败了')
    console.log('========================================')
    console.log('💡 建议的解决步骤:')
    console.log('1. 确认MySQL在WSL中正确运行')
    console.log('2. 检查MySQL bind-address配置')
    console.log('3. 检查Windows防火墙设置')
    console.log('4. 尝试重启WSL和MySQL服务')
  }
}

// 运行诊断
main().catch(error => {
  console.error('诊断过程中发生错误:', error)
})
