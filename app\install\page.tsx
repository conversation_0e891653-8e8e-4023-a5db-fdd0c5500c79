"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Progress } from "@/components/ui/progress"
import { CheckCircle, AlertCircle, Database, Settings, User, Globe, Loader2 } from "lucide-react"

interface InstallStep {
  id: string
  title: string
  description: string
  icon: React.ReactNode
  completed: boolean
}

interface DatabaseConfig {
  host: string
  port: string
  user: string
  password: string
  database: string
}

interface AdminConfig {
  username: string
  password: string
  email: string
}

interface SiteConfig {
  siteName: string
  siteUrl: string
  adminEmail: string
}

export default function InstallPage() {
  const [currentStep, setCurrentStep] = useState(0)
  const [installing, setInstalling] = useState(false)
  const [installProgress, setInstallProgress] = useState(0)
  const [error, setError] = useState("")
  const [success, setSuccess] = useState("")

  const [databaseConfig, setDatabaseConfig] = useState<DatabaseConfig>({
    host: "localhost",
    port: "3306",
    user: "root",
    password: "",
    database: "dianfuto"
  })

  const [adminConfig, setAdminConfig] = useState<AdminConfig>({
    username: "admin",
    password: "admin123456",
    email: "<EMAIL>"
  })

  const [siteConfig, setSiteConfig] = useState<SiteConfig>({
    siteName: "滇护通",
    siteUrl: "http://localhost:3003",
    adminEmail: "<EMAIL>"
  })

  const [installSteps, setInstallSteps] = useState<InstallStep[]>([
    {
      id: "welcome",
      title: "欢迎使用",
      description: "欢迎使用滇护通安装向导",
      icon: <Globe className="w-6 h-6" />,
      completed: false
    },
    {
      id: "database",
      title: "数据库配置",
      description: "配置数据库连接信息",
      icon: <Database className="w-6 h-6" />,
      completed: false
    },
    {
      id: "admin",
      title: "管理员账户",
      description: "创建管理员账户",
      icon: <User className="w-6 h-6" />,
      completed: false
    },
    {
      id: "site",
      title: "网站配置",
      description: "配置网站基本信息",
      icon: <Settings className="w-6 h-6" />,
      completed: false
    },
    {
      id: "install",
      title: "开始安装",
      description: "执行安装程序",
      icon: <CheckCircle className="w-6 h-6" />,
      completed: false
    }
  ])

  // 检查是否已安装
  useEffect(() => {
    checkInstallStatus()
  }, [])

  const checkInstallStatus = async () => {
    try {
      const response = await fetch('/api/install/check')
      const data = await response.json()
      if (data.installed) {
        setSuccess("项目已经安装完成！")
        // 可以重定向到主页或管理后台
      }
    } catch (error) {
      // 未安装，继续安装流程
    }
  }

  const testDatabaseConnection = async () => {
    try {
      setError("")
      setSuccess("")

      // 验证输入
      if (!databaseConfig.host || !databaseConfig.port || !databaseConfig.user || !databaseConfig.database) {
        setError("请填写完整的数据库连接信息")
        return false
      }

      // 创建AbortController用于超时控制
      const controller = new AbortController()
      const timeoutId = setTimeout(() => controller.abort(), 30000) // 30秒超时

      const response = await fetch('/api/install/test-db', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(databaseConfig),
        signal: controller.signal
      })

      clearTimeout(timeoutId)

      const data = await response.json()

      if (data.success) {
        setSuccess(`数据库连接测试成功！${data.databaseExists ? '数据库已存在' : '数据库已创建'}`)
        return true
      } else {
        let errorMsg = data.error || "数据库连接失败"
        if (data.suggestion) {
          errorMsg += `\n\n💡 建议: ${data.suggestion}`
        }
        if (data.code) {
          errorMsg += `\n\n错误代码: ${data.code}`
        }
        setError(errorMsg)
        console.error('数据库连接失败详情:', data)
        return false
      }
    } catch (error: any) {
      console.error('数据库连接测试异常:', error)

      let errorMessage = '数据库连接测试失败'

      if (error.name === 'AbortError') {
        errorMessage = '数据库连接超时（30秒），请检查数据库服务器状态和网络连接'
      } else if (error.message?.includes('fetch')) {
        errorMessage = '网络请求失败，请检查服务器是否正常运行'
      } else {
        errorMessage = `连接测试失败: ${error.message || '未知错误'}`
      }

      setError(errorMessage)
      return false
    }
  }

  const executeInstallation = async () => {
    setInstalling(true)
    setInstallProgress(0)
    setError("")

    try {
      // 步骤1: 创建环境配置文件
      setInstallProgress(20)
      console.log('开始创建环境配置文件...')
      const envResponse = await fetch('/api/install/create-env', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          database: databaseConfig,
          site: siteConfig
        })
      })

      const envData = await envResponse.json()
      if (!envData.success) {
        throw new Error(envData.error || "环境配置文件创建失败")
      }
      console.log('环境配置文件创建成功')

      // 步骤2: 创建数据库表
      setInstallProgress(40)
      console.log('开始创建数据库表...')
      const tablesResponse = await fetch('/api/install/create-tables', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(databaseConfig)
      })

      const tablesData = await tablesResponse.json()
      if (!tablesData.success) {
        throw new Error(tablesData.error || "数据库表创建失败")
      }
      console.log('数据库表创建成功，创建了', tablesData.createdTables, '个表')

      // 步骤3: 初始化数据
      setInstallProgress(60)
      const dataResponse = await fetch('/api/install/init-data', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          admin: adminConfig,
          site: siteConfig
        })
      })

      if (!dataResponse.ok) {
        throw new Error("初始数据创建失败")
      }

      // 步骤4: 创建上传目录
      setInstallProgress(80)
      const dirsResponse = await fetch('/api/install/create-dirs', {
        method: 'POST'
      })

      if (!dirsResponse.ok) {
        throw new Error("目录创建失败")
      }

      // 步骤5: 完成安装
      setInstallProgress(100)
      const completeResponse = await fetch('/api/install/complete', {
        method: 'POST'
      })

      if (!completeResponse.ok) {
        throw new Error("安装完成标记失败")
      }

      setSuccess("安装完成！正在跳转到管理后台...")
      
      // 3秒后跳转到管理后台
      setTimeout(() => {
        window.location.href = '/admin'
      }, 3000)

    } catch (error) {
      setError(error instanceof Error ? error.message : "安装失败")
      setInstallProgress(0)
    } finally {
      setInstalling(false)
    }
  }

  const nextStep = async () => {
    if (currentStep === 1) {
      // 数据库配置步骤，测试连接
      const connected = await testDatabaseConnection()
      if (!connected) return
    }

    if (currentStep < installSteps.length - 1) {
      const newSteps = [...installSteps]
      newSteps[currentStep].completed = true
      setInstallSteps(newSteps)
      setCurrentStep(currentStep + 1)
      setError("")
      setSuccess("")
    } else {
      // 开始安装
      await executeInstallation()
    }
  }

  const prevStep = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1)
      setError("")
      setSuccess("")
    }
  }

  const renderStepContent = () => {
    switch (currentStep) {
      case 0:
        return (
          <div className="text-center space-y-6">
            <div className="w-24 h-24 bg-emerald-100 rounded-full flex items-center justify-center mx-auto">
              <Globe className="w-12 h-12 text-emerald-600" />
            </div>
            <div>
              <h2 className="text-2xl font-bold text-gray-900 mb-4">欢迎使用滇护通安装向导</h2>
              <p className="text-gray-600 mb-6">
                本向导将帮助您完成滇护通医疗陪护服务平台的安装和配置。
                整个过程大约需要5-10分钟。
              </p>
              <div className="bg-blue-50 p-4 rounded-lg text-left">
                <h3 className="font-semibold text-blue-900 mb-2">安装前准备：</h3>
                <ul className="text-blue-800 space-y-1 text-sm">
                  <li>• 确保MySQL数据库服务正在运行</li>
                  <li>• 准备数据库用户名和密码</li>
                  <li>• 确保有足够的磁盘空间（至少100MB）</li>
                  <li>• 确保Node.js环境正常运行</li>
                </ul>
              </div>
            </div>
          </div>
        )

      case 1:
        return (
          <div className="space-y-6">
            <div className="text-center mb-6">
              <Database className="w-12 h-12 text-emerald-600 mx-auto mb-4" />
              <h2 className="text-2xl font-bold text-gray-900">数据库配置</h2>
              <p className="text-gray-600">请填写数据库连接信息</p>
            </div>
            
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="host">数据库主机</Label>
                <Input
                  id="host"
                  value={databaseConfig.host}
                  onChange={(e) => setDatabaseConfig({...databaseConfig, host: e.target.value})}
                  placeholder="localhost"
                />
              </div>
              <div>
                <Label htmlFor="port">端口</Label>
                <Input
                  id="port"
                  value={databaseConfig.port}
                  onChange={(e) => setDatabaseConfig({...databaseConfig, port: e.target.value})}
                  placeholder="3306"
                />
              </div>
            </div>

            <div>
              <Label htmlFor="database">数据库名称</Label>
              <Input
                id="database"
                value={databaseConfig.database}
                onChange={(e) => setDatabaseConfig({...databaseConfig, database: e.target.value})}
                placeholder="dianfuto"
              />
            </div>

            <div>
              <Label htmlFor="user">用户名</Label>
              <Input
                id="user"
                value={databaseConfig.user}
                onChange={(e) => setDatabaseConfig({...databaseConfig, user: e.target.value})}
                placeholder="root"
              />
            </div>

            <div>
              <Label htmlFor="password">密码</Label>
              <Input
                id="password"
                type="password"
                value={databaseConfig.password}
                onChange={(e) => setDatabaseConfig({...databaseConfig, password: e.target.value})}
                placeholder="请输入数据库密码"
              />
            </div>

            <Button 
              onClick={testDatabaseConnection}
              variant="outline"
              className="w-full"
            >
              测试数据库连接
            </Button>
          </div>
        )

      case 2:
        return (
          <div className="space-y-6">
            <div className="text-center mb-6">
              <User className="w-12 h-12 text-emerald-600 mx-auto mb-4" />
              <h2 className="text-2xl font-bold text-gray-900">管理员账户</h2>
              <p className="text-gray-600">创建系统管理员账户</p>
            </div>

            <div>
              <Label htmlFor="username">用户名</Label>
              <Input
                id="username"
                value={adminConfig.username}
                onChange={(e) => setAdminConfig({...adminConfig, username: e.target.value})}
                placeholder="admin"
              />
            </div>

            <div>
              <Label htmlFor="adminPassword">密码</Label>
              <Input
                id="adminPassword"
                type="password"
                value={adminConfig.password}
                onChange={(e) => setAdminConfig({...adminConfig, password: e.target.value})}
                placeholder="请输入管理员密码"
              />
              <p className="text-sm text-gray-500 mt-1">密码长度至少8位，建议包含字母和数字</p>
            </div>

            <div>
              <Label htmlFor="adminEmail">邮箱</Label>
              <Input
                id="adminEmail"
                type="email"
                value={adminConfig.email}
                onChange={(e) => setAdminConfig({...adminConfig, email: e.target.value})}
                placeholder="<EMAIL>"
              />
            </div>
          </div>
        )

      case 3:
        return (
          <div className="space-y-6">
            <div className="text-center mb-6">
              <Settings className="w-12 h-12 text-emerald-600 mx-auto mb-4" />
              <h2 className="text-2xl font-bold text-gray-900">网站配置</h2>
              <p className="text-gray-600">配置网站基本信息</p>
            </div>

            <div>
              <Label htmlFor="siteName">网站名称</Label>
              <Input
                id="siteName"
                value={siteConfig.siteName}
                onChange={(e) => setSiteConfig({...siteConfig, siteName: e.target.value})}
                placeholder="滇护通"
              />
            </div>

            <div>
              <Label htmlFor="siteUrl">网站地址</Label>
              <Input
                id="siteUrl"
                value={siteConfig.siteUrl}
                onChange={(e) => setSiteConfig({...siteConfig, siteUrl: e.target.value})}
                placeholder="http://localhost:3003"
              />
            </div>

            <div>
              <Label htmlFor="contactEmail">联系邮箱</Label>
              <Input
                id="contactEmail"
                type="email"
                value={siteConfig.adminEmail}
                onChange={(e) => setSiteConfig({...siteConfig, adminEmail: e.target.value})}
                placeholder="<EMAIL>"
              />
            </div>
          </div>
        )

      case 4:
        return (
          <div className="space-y-6">
            <div className="text-center mb-6">
              <CheckCircle className="w-12 h-12 text-emerald-600 mx-auto mb-4" />
              <h2 className="text-2xl font-bold text-gray-900">准备安装</h2>
              <p className="text-gray-600">确认配置信息并开始安装</p>
            </div>

            <div className="space-y-4">
              <div className="bg-gray-50 p-4 rounded-lg">
                <h3 className="font-semibold mb-2">数据库配置</h3>
                <p className="text-sm text-gray-600">主机: {databaseConfig.host}:{databaseConfig.port}</p>
                <p className="text-sm text-gray-600">数据库: {databaseConfig.database}</p>
                <p className="text-sm text-gray-600">用户: {databaseConfig.user}</p>
              </div>

              <div className="bg-gray-50 p-4 rounded-lg">
                <h3 className="font-semibold mb-2">管理员账户</h3>
                <p className="text-sm text-gray-600">用户名: {adminConfig.username}</p>
                <p className="text-sm text-gray-600">邮箱: {adminConfig.email}</p>
              </div>

              <div className="bg-gray-50 p-4 rounded-lg">
                <h3 className="font-semibold mb-2">网站配置</h3>
                <p className="text-sm text-gray-600">网站名称: {siteConfig.siteName}</p>
                <p className="text-sm text-gray-600">网站地址: {siteConfig.siteUrl}</p>
              </div>
            </div>

            {installing && (
              <div className="space-y-4">
                <Progress value={installProgress} className="w-full" />
                <p className="text-center text-sm text-gray-600">
                  安装进度: {installProgress}%
                </p>
              </div>
            )}
          </div>
        )

      default:
        return null
    }
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-emerald-50 to-teal-50 py-12 px-4">
      <div className="max-w-4xl mx-auto">
        {/* 标题 */}
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">滇护通安装向导</h1>
          <p className="text-gray-600">医疗陪护服务平台安装程序</p>
        </div>

        {/* 步骤指示器 */}
        <div className="mb-8">
          <div className="flex justify-between items-center">
            {installSteps.map((step, index) => (
              <div key={step.id} className="flex flex-col items-center">
                <div className={`w-12 h-12 rounded-full flex items-center justify-center mb-2 ${
                  index === currentStep 
                    ? 'bg-emerald-600 text-white' 
                    : step.completed 
                      ? 'bg-green-600 text-white' 
                      : 'bg-gray-200 text-gray-500'
                }`}>
                  {step.completed ? <CheckCircle className="w-6 h-6" /> : step.icon}
                </div>
                <span className={`text-xs text-center ${
                  index === currentStep ? 'text-emerald-600 font-semibold' : 'text-gray-500'
                }`}>
                  {step.title}
                </span>
              </div>
            ))}
          </div>
        </div>

        {/* 主要内容 */}
        <Card className="shadow-xl">
          <CardHeader>
            <CardTitle className="text-center">
              {installSteps[currentStep]?.title}
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            {/* 错误提示 */}
            {error && (
              <Alert variant="destructive">
                <AlertCircle className="h-4 w-4" />
                <AlertDescription>{error}</AlertDescription>
              </Alert>
            )}

            {/* 成功提示 */}
            {success && (
              <Alert className="border-green-200 bg-green-50">
                <CheckCircle className="h-4 w-4 text-green-600" />
                <AlertDescription className="text-green-800">{success}</AlertDescription>
              </Alert>
            )}

            {/* 步骤内容 */}
            {renderStepContent()}

            {/* 导航按钮 */}
            <div className="flex justify-between pt-6">
              <Button
                onClick={prevStep}
                variant="outline"
                disabled={currentStep === 0 || installing}
              >
                上一步
              </Button>
              
              <Button
                onClick={nextStep}
                disabled={installing}
                className="bg-emerald-600 hover:bg-emerald-700"
              >
                {installing ? (
                  <>
                    <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                    安装中...
                  </>
                ) : currentStep === installSteps.length - 1 ? (
                  "开始安装"
                ) : (
                  "下一步"
                )}
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
