@echo off
chcp 65001 >nul
echo 🔧 滇护通安装问题修复脚本 (Windows版)
echo ================================

REM 检查当前目录
if not exist "package.json" (
    echo ❌ 错误: 请在项目根目录运行此脚本
    pause
    exit /b 1
)

echo 📍 当前目录: %CD%

REM 1. 检查Node.js和npm
echo.
echo 1. 检查Node.js环境...
node --version >nul 2>&1
if %errorlevel% equ 0 (
    for /f "tokens=*" %%i in ('node --version') do echo ✅ Node.js版本: %%i
) else (
    echo ❌ Node.js未安装
    pause
    exit /b 1
)

npm --version >nul 2>&1
if %errorlevel% equ 0 (
    for /f "tokens=*" %%i in ('npm --version') do echo ✅ npm版本: %%i
) else (
    echo ❌ npm未安装
    pause
    exit /b 1
)

REM 2. 检查MySQL (如果安装了)
echo.
echo 2. 检查MySQL...
mysql --version >nul 2>&1
if %errorlevel% equ 0 (
    for /f "tokens=*" %%i in ('mysql --version') do echo ✅ MySQL已安装: %%i
) else (
    echo ⚠️  MySQL未在PATH中找到，请确保MySQL已安装并正在运行
)

REM 3. 清理和重新安装依赖
echo.
echo 3. 重新安装项目依赖...
if exist "node_modules" (
    echo 🗑️  删除现有node_modules...
    rmdir /s /q node_modules
)

if exist "package-lock.json" (
    echo 🗑️  删除package-lock.json...
    del package-lock.json
)

if exist "yarn.lock" (
    echo 🗑️  删除yarn.lock...
    del yarn.lock
)

echo 📦 安装依赖...
npm install

if %errorlevel% equ 0 (
    echo ✅ 依赖安装成功
) else (
    echo ❌ 依赖安装失败
    pause
    exit /b 1
)

REM 4. 构建项目
echo.
echo 4. 构建项目...
npm run build

if %errorlevel% equ 0 (
    echo ✅ 项目构建成功
) else (
    echo ❌ 项目构建失败
    pause
    exit /b 1
)

REM 5. 检查环境配置
echo.
echo 5. 检查环境配置...
if exist ".env.local" (
    echo ✅ 环境配置文件存在
) else (
    echo ⚠️  环境配置文件不存在
    echo 💡 请运行安装程序创建配置文件
)

REM 6. 创建必要的目录
echo.
echo 6. 创建必要的目录...
if not exist "public\uploads" mkdir public\uploads
if not exist "public\uploads\images" mkdir public\uploads\images
if not exist "logs" mkdir logs
echo ✅ 目录创建完成

REM 7. 检查端口占用
echo.
echo 7. 检查端口占用...
netstat -an | findstr ":3000" >nul
if %errorlevel% equ 0 (
    echo ⚠️  端口3000可能已被占用
    echo 🔍 请检查是否有其他程序使用此端口
) else (
    echo ✅ 端口3000可用
)

REM 8. 生成修复报告
echo.
echo 8. 生成修复报告...
echo 滇护通安装修复报告 > fix-report.txt
echo 生成时间: %date% %time% >> fix-report.txt
echo 项目路径: %CD% >> fix-report.txt
echo. >> fix-report.txt
echo 修复操作: >> fix-report.txt
echo ✅ 重新安装依赖 >> fix-report.txt
echo ✅ 重新构建项目 >> fix-report.txt
echo ✅ 创建必要目录 >> fix-report.txt
echo. >> fix-report.txt
echo 下一步操作: >> fix-report.txt
echo 1. 启动项目: npm start >> fix-report.txt
echo 2. 访问安装程序: http://localhost:3000/install >> fix-report.txt
echo 3. 按向导完成安装 >> fix-report.txt

echo ✅ 修复报告已保存到 fix-report.txt

echo.
echo 🎉 修复脚本执行完成！
echo.
echo 📋 下一步操作:
echo 1. 启动项目: npm start
echo 2. 访问安装程序: http://localhost:3000/install
echo 3. 按向导完成安装
echo.
echo 📞 如果仍有问题，请查看 fix-report.txt 文件

pause
