// 数据库连接超时问题测试脚本
const mysql = require('mysql2/promise')
const { execSync } = require('child_process')

async function testTimeoutFix() {
  console.log('🔧 数据库连接超时问题诊断')
  console.log('================================')
  
  // 1. 环境检查
  console.log('\n1. 检查运行环境...')
  console.log('✅ 运行在Windows/WSL环境')
  console.log('✅ 目标数据库: *************:22929')
  console.log('✅ 数据库用户: root')

  // 2. 跳过系统检查，直接测试连接
  
  // 3. 测试不同的连接参数
  console.log('\n3. 测试不同的连接参数...')
  
  const testConfigs = [
    { host: '*************', port: 22929, timeout: 5000, desc: 'WSL数据库 - 5秒超时' },
    { host: '*************', port: 22929, timeout: 10000, desc: 'WSL数据库 - 10秒超时' },
    { host: '*************', port: 22929, timeout: 30000, desc: 'WSL数据库 - 30秒超时' },
    { host: '127.0.0.1', port: 3306, timeout: 10000, desc: '本地MySQL - 10秒超时' },
    { host: 'localhost', port: 3306, timeout: 10000, desc: 'localhost - 10秒超时' },
  ]

  const password = 'cefd14ff2eecd2aa' // WSL数据库密码
  
  for (const config of testConfigs) {
    console.log(`\n🔍 测试: ${config.desc}`)
    console.log(`   主机: ${config.host}:${config.port || 3306}, 超时: ${config.timeout}ms`)

    const startTime = Date.now()

    try {
      const connection = await mysql.createConnection({
        host: config.host,
        port: config.port || 3306,
        user: 'root',
        password: password,
        connectTimeout: config.timeout,
      })

      const connectTime = Date.now() - startTime
      console.log(`✅ 连接成功 (${connectTime}ms)`)

      // 测试ping
      const pingStart = Date.now()
      await connection.ping()
      const pingTime = Date.now() - pingStart
      console.log(`✅ Ping成功 (${pingTime}ms)`)

      // 测试简单查询
      const queryStart = Date.now()
      const [rows] = await connection.query('SELECT 1 as test, NOW() as current_time')
      const queryTime = Date.now() - queryStart
      console.log(`✅ 查询成功 (${queryTime}ms)`)
      console.log(`   结果:`, rows[0])

      await connection.end()

      // 如果这个配置成功，推荐使用
      console.log(`\n🎉 推荐配置找到！`)
      console.log(`📋 在安装程序中使用以下配置:`)
      console.log(`   数据库主机: ${config.host}`)
      console.log(`   端口: ${config.port || 3306}`)
      console.log(`   用户名: root`)
      console.log(`   密码: cefd14ff2eecd2aa`)
      console.log(`   数据库名: dianfuto`)

      return config.host

    } catch (error) {
      const errorTime = Date.now() - startTime
      console.log(`❌ 连接失败 (${errorTime}ms)`)
      console.log(`   错误: ${error.code} - ${error.message}`)
      
      // 分析错误原因
      switch (error.code) {
        case 'ETIMEDOUT':
          console.log('   原因: 连接超时，可能是网络问题或MySQL配置问题')
          break
        case 'ECONNREFUSED':
          console.log('   原因: 连接被拒绝，MySQL可能未在此地址监听')
          break
        case 'ER_ACCESS_DENIED_ERROR':
          console.log('   原因: 访问被拒绝，用户名或密码错误')
          break
        case 'ENOTFOUND':
          console.log('   原因: 主机名无法解析')
          break
        default:
          console.log(`   原因: 未知错误 (${error.code})`)
      }
    }
  }
  
  // 4. 如果所有配置都失败，提供解决方案
  console.log('\n❌ 所有连接配置都失败了')
  console.log('\n🔧 建议的解决步骤:')
  console.log('\n步骤1: 检查MySQL配置')
  console.log('sudo nano /etc/mysql/mysql.conf.d/mysqld.cnf')
  console.log('找到 bind-address 行，修改为:')
  console.log('bind-address = 0.0.0.0')
  console.log('或者注释掉该行')
  
  console.log('\n步骤2: 重启MySQL服务')
  console.log('sudo systemctl restart mysql')
  
  console.log('\n步骤3: 检查用户权限')
  console.log('mysql -u root -p')
  console.log("CREATE USER 'root'@'%' IDENTIFIED BY 'your_password';")
  console.log("GRANT ALL PRIVILEGES ON *.* TO 'root'@'%';")
  console.log('FLUSH PRIVILEGES;')
  
  console.log('\n步骤4: 检查防火墙')
  console.log('sudo ufw allow 3306')
  
  console.log('\n步骤5: 运行自动修复脚本')
  console.log('sudo bash scripts/fix-mysql-timeout.sh')
  
  console.log('\n📞 如果问题仍然存在:')
  console.log('1. 检查MySQL错误日志: sudo tail -f /var/log/mysql/error.log')
  console.log('2. 检查系统日志: sudo journalctl -u mysql -f')
  console.log('3. 尝试重新安装MySQL')
  
  return null
}

// 运行测试
testTimeoutFix().then(result => {
  if (result) {
    console.log(`\n✅ 测试完成！推荐使用主机地址: ${result}`)
  } else {
    console.log('\n❌ 测试完成，但未找到可用的连接配置')
    console.log('请按照上述建议进行故障排除')
  }
}).catch(error => {
  console.error('\n💥 测试过程中发生严重错误:', error)
  console.log('\n请检查:')
  console.log('1. Node.js是否正确安装')
  console.log('2. mysql2包是否已安装: npm install mysql2')
  console.log('3. 是否在项目根目录运行此脚本')
})
