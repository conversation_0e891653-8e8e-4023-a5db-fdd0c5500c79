# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
/node_modules

# next.js
/.next/
/out/

# production
/build

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*

# env files
.env*

# vercel
.vercel

# typescript
*.tsbuildinfo
next-env.d.ts

# temporary files and reports
*_REPORT.md
*_FIXES_REPORT.md
*_OPTIMIZATION_REPORT.md
*_FUNCTIONALITY_REPORT.md

# test and debug scripts
scripts/test-*.js
scripts/test-*.png
scripts/fix-*.sh
scripts/fix-*.bat
scripts/diagnose-*.js
scripts/verify-*.js
scripts/find-*.js

# uploads (keep only essential ones)
public/uploads/temp-*
public/uploads/*-[0-9]*-[0-9]*.jpg
public/uploads/*-[0-9]*-[0-9]*.png

# package manager files
yarn.lock
package-lock.json

# IDE and editor files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db