// 查找phpMyAdmin的实际路径
const http = require('http')

function testPath(baseUrl, path) {
  return new Promise((resolve) => {
    const url = `${baseUrl}${path}`
    console.log(`测试: ${url}`)
    
    const req = http.get(url, (res) => {
      let data = ''
      res.on('data', chunk => data += chunk)
      res.on('end', () => {
        const isPhpMyAdmin = data.includes('phpMyAdmin') || 
                           data.includes('pma_') || 
                           data.includes('mysql') ||
                           data.includes('database') ||
                           data.includes('login') ||
                           data.includes('phpmyadmin')
        
        console.log(`  状态: ${res.statusCode}, phpMyAdmin: ${isPhpMyAdmin ? '✅' : '❌'}`)
        
        if (isPhpMyAdmin) {
          console.log(`  🎉 找到phpMyAdmin!`)
          console.log(`  内容片段: ${data.substring(0, 100)}...`)
        }
        
        resolve({ url, status: res.statusCode, isPhpMyAdmin, data: data.substring(0, 200) })
      })
    })
    
    req.on('error', (error) => {
      console.log(`  ❌ 错误: ${error.message}`)
      resolve({ url, error: error.message })
    })
    
    req.setTimeout(5000, () => {
      req.destroy()
      console.log(`  ⏰ 超时`)
      resolve({ url, error: 'timeout' })
    })
  })
}

async function findPhpMyAdmin() {
  console.log('🔍 查找phpMyAdmin实际路径')
  console.log('================================')
  
  const baseUrl = 'http://172.31.97.108:22929'
  
  // 常见的phpMyAdmin路径
  const paths = [
    '/',
    '/phpmyadmin',
    '/phpMyAdmin',
    '/pma',
    '/mysql',
    '/database',
    '/admin',
    '/db',
    '/phpmyadmin/',
    '/phpMyAdmin/',
    '/pma/',
    '/mysql/',
    '/database/',
    '/admin/',
    '/db/',
    '/bt_phpmyadmin',  // 宝塔面板常用路径
    '/btphpmyadmin',
    '/baota',
    '/bt'
  ]
  
  console.log(`基础URL: ${baseUrl}`)
  console.log(`测试 ${paths.length} 个可能的路径...\n`)
  
  const results = []
  
  for (const path of paths) {
    const result = await testPath(baseUrl, path)
    results.push(result)
    
    if (result.isPhpMyAdmin) {
      console.log(`\n🎉 找到phpMyAdmin!`)
      console.log(`完整URL: ${result.url}`)
      console.log(`状态码: ${result.status}`)
      break
    }
    
    // 添加小延迟避免过快请求
    await new Promise(resolve => setTimeout(resolve, 100))
  }
  
  console.log('\n================================')
  console.log('📋 测试结果汇总')
  console.log('================================')
  
  const found = results.find(r => r.isPhpMyAdmin)
  
  if (found) {
    console.log(`✅ phpMyAdmin找到: ${found.url}`)
    console.log('\n现在我们知道了正确的访问地址，但这并不能直接解决MySQL连接问题。')
    console.log('phpMyAdmin通过HTTP访问，而我们需要直接的MySQL连接。')
    
    console.log('\n🔧 可能的解决方案:')
    console.log('1. MySQL可能运行在标准端口3306上')
    console.log('2. 或者运行在其他端口上')
    console.log('3. 需要检查实际的MySQL端口配置')
    
  } else {
    console.log('❌ 未找到phpMyAdmin')
    console.log('\n可能的原因:')
    console.log('1. phpMyAdmin在不同的端口上')
    console.log('2. 需要特殊的访问路径或参数')
    console.log('3. 服务配置问题')
    
    console.log('\n📊 状态码统计:')
    const statusCounts = {}
    results.forEach(r => {
      if (r.status) {
        statusCounts[r.status] = (statusCounts[r.status] || 0) + 1
      }
    })
    console.log(statusCounts)
  }
  
  console.log('\n💡 下一步建议:')
  console.log('1. 检查MySQL是否在标准端口3306运行')
  console.log('2. 查看宝塔面板的数据库配置')
  console.log('3. 尝试连接到localhost:3306')
  console.log('4. 检查WSL内部的MySQL服务')
}

// 运行查找
findPhpMyAdmin().catch(error => {
  console.error('查找过程中发生错误:', error)
})
