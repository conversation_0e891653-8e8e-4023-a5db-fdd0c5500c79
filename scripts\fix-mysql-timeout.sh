#!/bin/bash

echo "🔧 MySQL连接超时问题修复脚本"
echo "================================"

# 检查当前用户权限
if [ "$EUID" -ne 0 ]; then
    echo "⚠️  此脚本需要root权限，请使用sudo运行"
    echo "使用方法: sudo bash scripts/fix-mysql-timeout.sh"
    exit 1
fi

echo "📍 当前目录: $(pwd)"

# 1. 检查MySQL服务状态
echo ""
echo "1. 检查MySQL服务状态..."
if systemctl is-active --quiet mysql; then
    echo "✅ MySQL服务正在运行"
    systemctl status mysql --no-pager -l
else
    echo "❌ MySQL服务未运行，正在启动..."
    systemctl start mysql
    if systemctl is-active --quiet mysql; then
        echo "✅ MySQL服务启动成功"
    else
        echo "❌ MySQL服务启动失败"
        systemctl status mysql --no-pager -l
        exit 1
    fi
fi

# 2. 检查MySQL监听端口和地址
echo ""
echo "2. 检查MySQL监听配置..."
echo "当前MySQL监听端口:"
netstat -tlnp | grep :3306 || echo "❌ MySQL未监听3306端口"

echo ""
echo "检查MySQL配置文件..."
MYSQL_CONFIG="/etc/mysql/mysql.conf.d/mysqld.cnf"
if [ -f "$MYSQL_CONFIG" ]; then
    echo "✅ 找到MySQL配置文件: $MYSQL_CONFIG"
    
    # 检查bind-address设置
    if grep -q "^bind-address" "$MYSQL_CONFIG"; then
        echo "当前bind-address设置:"
        grep "^bind-address" "$MYSQL_CONFIG"
        
        echo ""
        echo "🔧 修改bind-address以允许所有连接..."
        # 备份原配置
        cp "$MYSQL_CONFIG" "$MYSQL_CONFIG.backup.$(date +%Y%m%d_%H%M%S)"
        echo "✅ 配置文件已备份"
        
        # 修改bind-address
        sed -i 's/^bind-address.*/bind-address = 0.0.0.0/' "$MYSQL_CONFIG"
        echo "✅ bind-address已设置为0.0.0.0"
    else
        echo "⚠️  未找到bind-address设置，添加配置..."
        echo "" >> "$MYSQL_CONFIG"
        echo "# Allow connections from all addresses" >> "$MYSQL_CONFIG"
        echo "bind-address = 0.0.0.0" >> "$MYSQL_CONFIG"
        echo "✅ 已添加bind-address = 0.0.0.0"
    fi
    
    # 检查其他重要设置
    echo ""
    echo "检查其他MySQL设置..."
    
    # 添加超时设置
    if ! grep -q "wait_timeout" "$MYSQL_CONFIG"; then
        echo "" >> "$MYSQL_CONFIG"
        echo "# Timeout settings" >> "$MYSQL_CONFIG"
        echo "wait_timeout = 600" >> "$MYSQL_CONFIG"
        echo "interactive_timeout = 600" >> "$MYSQL_CONFIG"
        echo "connect_timeout = 60" >> "$MYSQL_CONFIG"
        echo "✅ 已添加超时设置"
    fi
    
else
    echo "❌ 未找到MySQL配置文件"
fi

# 3. 重启MySQL服务
echo ""
echo "3. 重启MySQL服务以应用配置..."
systemctl restart mysql

if systemctl is-active --quiet mysql; then
    echo "✅ MySQL服务重启成功"
else
    echo "❌ MySQL服务重启失败"
    systemctl status mysql --no-pager -l
    exit 1
fi

# 4. 验证MySQL监听状态
echo ""
echo "4. 验证MySQL监听状态..."
sleep 3
netstat -tlnp | grep :3306
if netstat -tlnp | grep -q ":3306.*0.0.0.0"; then
    echo "✅ MySQL正在监听所有地址的3306端口"
elif netstat -tlnp | grep -q ":3306"; then
    echo "⚠️  MySQL正在监听3306端口，但可能只监听特定地址"
else
    echo "❌ MySQL未监听3306端口"
fi

# 5. 检查防火墙设置
echo ""
echo "5. 检查防火墙设置..."
if command -v ufw &> /dev/null; then
    echo "检查ufw防火墙状态:"
    ufw status
    
    if ufw status | grep -q "Status: active"; then
        echo "🔧 允许MySQL端口通过防火墙..."
        ufw allow 3306
        echo "✅ 已允许3306端口"
    else
        echo "✅ ufw防火墙未启用"
    fi
else
    echo "⚠️  未找到ufw防火墙"
fi

# 6. 创建测试用户和设置权限
echo ""
echo "6. 设置MySQL用户权限..."
echo "请输入MySQL root密码:"
read -s MYSQL_ROOT_PASSWORD

mysql -u root -p"$MYSQL_ROOT_PASSWORD" << EOF
-- 创建或更新root用户的远程访问权限
CREATE USER IF NOT EXISTS 'root'@'%' IDENTIFIED BY '$MYSQL_ROOT_PASSWORD';
GRANT ALL PRIVILEGES ON *.* TO 'root'@'%' WITH GRANT OPTION;

-- 确保localhost访问也正常
GRANT ALL PRIVILEGES ON *.* TO 'root'@'localhost' WITH GRANT OPTION;
GRANT ALL PRIVILEGES ON *.* TO 'root'@'127.0.0.1' WITH GRANT OPTION;

-- 刷新权限
FLUSH PRIVILEGES;

-- 显示用户权限
SELECT User, Host FROM mysql.user WHERE User = 'root';
EOF

if [ $? -eq 0 ]; then
    echo "✅ MySQL用户权限设置成功"
else
    echo "❌ MySQL用户权限设置失败，请检查密码是否正确"
fi

# 7. 测试连接
echo ""
echo "7. 测试数据库连接..."

# 测试不同的主机地址
TEST_HOSTS=("127.0.0.1" "localhost" "0.0.0.0")

for host in "${TEST_HOSTS[@]}"; do
    echo "测试连接到 $host..."
    if timeout 10 mysql -h "$host" -u root -p"$MYSQL_ROOT_PASSWORD" -e "SELECT 1;" &>/dev/null; then
        echo "✅ $host - 连接成功"
    else
        echo "❌ $host - 连接失败"
    fi
done

# 8. 生成修复报告
echo ""
echo "================================"
echo "📋 修复完成报告"
echo "================================"
echo "修复时间: $(date)"
echo ""
echo "已执行的修复操作:"
echo "✅ 检查并启动MySQL服务"
echo "✅ 修改bind-address为0.0.0.0"
echo "✅ 添加超时配置"
echo "✅ 重启MySQL服务"
echo "✅ 配置防火墙规则"
echo "✅ 设置用户权限"
echo ""
echo "📝 下一步操作:"
echo "1. 在安装程序中使用以下数据库配置:"
echo "   主机: 127.0.0.1"
echo "   端口: 3306"
echo "   用户名: root"
echo "   密码: [您刚才输入的密码]"
echo ""
echo "2. 如果仍有问题，请运行:"
echo "   node scripts/test-wsl-db.js"
echo ""
echo "3. 重新访问安装页面:"
echo "   http://localhost:3000/install"

echo ""
echo "🎉 MySQL连接超时问题修复完成！"
