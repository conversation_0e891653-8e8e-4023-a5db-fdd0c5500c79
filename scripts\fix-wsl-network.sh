#!/bin/bash

echo "🔧 修复WSL网络和数据库连接问题"
echo "================================"

# 1. 检查当前网络配置
echo "1. 检查当前网络配置..."
echo "WSL IP地址:"
ip addr show eth0 | grep "inet " | awk '{print $2}'

echo ""
echo "默认网关:"
ip route | grep default

echo ""
echo "DNS配置:"
cat /etc/resolv.conf

# 2. 修复WSL网络配置
echo ""
echo "2. 修复WSL网络配置..."

# 创建WSL配置文件（在Windows侧）
echo "创建.wslconfig文件..."
cat > /mnt/c/Users/<USER>/.wslconfig << 'EOF'
[wsl2]
networkingMode=mirrored
dnsTunneling=true
firewall=true
autoProxy=false
EOF

echo "✅ 创建WSL配置文件"

# 3. 配置MySQL绑定所有接口
echo ""
echo "3. 配置MySQL绑定所有接口..."

# 备份原配置
cp /etc/mysql/mariadb.conf.d/50-server.cnf /etc/mysql/mariadb.conf.d/50-server.cnf.backup 2>/dev/null || true

# 创建新的MySQL配置
cat > /etc/mysql/mariadb.conf.d/50-server.cnf << 'EOF'
[server]

[mysqld]
user                    = mysql
pid-file                = /run/mysqld/mysqld.pid
socket                  = /run/mysqld/mysqld.sock
basedir                 = /usr
datadir                 = /var/lib/mysql
tmpdir                  = /tmp
lc-messages-dir         = /usr/share/mysql
skip-external-locking

# 网络配置 - 绑定所有接口
bind-address            = 0.0.0.0
port                    = 3306
skip-networking         = 0

# 日志配置
log-error               = /var/log/mysql/error.log

# 字符集
character-set-server    = utf8mb4
collation-server        = utf8mb4_general_ci

# 连接配置
max_connections         = 200
connect_timeout         = 60
wait_timeout            = 600
max_allowed_packet      = 64M

[embedded]

[mariadb]

[mariadb-10.6]
EOF

echo "✅ 更新MySQL配置"

# 4. 重启MySQL服务
echo ""
echo "4. 重启MySQL服务..."
systemctl stop mysql
sleep 3
systemctl start mysql
sleep 5

if systemctl is-active --quiet mysql; then
    echo "✅ MySQL服务重启成功"
else
    echo "❌ MySQL服务重启失败"
    systemctl status mysql
    exit 1
fi

# 5. 检查监听状态
echo ""
echo "5. 检查MySQL监听状态..."
netstat -tlnp | grep :3306

# 6. 重新设置用户权限
echo ""
echo "6. 重新设置用户权限..."

mysql -u root -pcefd14ff2eecd2aa << 'EOF'
-- 删除可能冲突的用户
DROP USER IF EXISTS 'root'@'%';
DROP USER IF EXISTS 'root'@'127.0.0.1';

-- 重新创建用户
CREATE USER 'root'@'%' IDENTIFIED BY 'cefd14ff2eecd2aa';
CREATE USER 'root'@'127.0.0.1' IDENTIFIED BY 'cefd14ff2eecd2aa';

-- 确保localhost用户正确
ALTER USER 'root'@'localhost' IDENTIFIED BY 'cefd14ff2eecd2aa';

-- 授予权限
GRANT ALL PRIVILEGES ON *.* TO 'root'@'localhost' WITH GRANT OPTION;
GRANT ALL PRIVILEGES ON *.* TO 'root'@'%' WITH GRANT OPTION;
GRANT ALL PRIVILEGES ON *.* TO 'root'@'127.0.0.1' WITH GRANT OPTION;

-- 刷新权限
FLUSH PRIVILEGES;

-- 显示用户
SELECT User, Host FROM mysql.user WHERE User = 'root';
EOF

echo "✅ 用户权限设置完成"

# 7. 测试连接
echo ""
echo "7. 测试数据库连接..."

echo "测试localhost连接:"
if mysql -u root -pcefd14ff2eecd2aa -h localhost -e "SELECT 'localhost连接成功' as result;" 2>/dev/null; then
    echo "✅ localhost连接成功"
else
    echo "❌ localhost连接失败"
fi

echo ""
echo "测试127.0.0.1连接:"
if mysql -u root -pcefd14ff2eecd2aa -h 127.0.0.1 -e "SELECT '127.0.0.1连接成功' as result;" 2>/dev/null; then
    echo "✅ 127.0.0.1连接成功"
else
    echo "❌ 127.0.0.1连接失败"
fi

# 8. 获取WSL IP用于Windows连接
echo ""
echo "8. 获取连接信息..."
WSL_IP=$(ip route get 1 | awk '{print $7; exit}')
echo "WSL IP地址: $WSL_IP"

echo ""
echo "🎉 网络和数据库修复完成！"
echo "================================"
echo "推荐的数据库连接配置:"
echo "主机: localhost (从WSL内部) 或 127.0.0.1 (从Windows)"
echo "端口: 3306"
echo "用户名: root"
echo "密码: cefd14ff2eecd2aa"
echo ""
echo "⚠️  重要提示:"
echo "1. 请重启WSL以应用网络配置: wsl --shutdown"
echo "2. 然后重新启动WSL"
echo "3. 重启后再测试数据库连接"
